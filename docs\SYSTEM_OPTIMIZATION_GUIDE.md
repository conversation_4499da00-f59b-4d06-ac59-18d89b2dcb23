# Hauling QR Trip System - System Optimization Guide

## Overview

This guide documents the comprehensive two-phase system resource optimization implemented for the Hauling QR Trip System deployment on a 4 vCPU / 8GB RAM VPS.

## Architecture

The optimization system uses a two-phase approach for maximum reliability:

- **Phase 0** (`optimize-system-resources.sh`): System-level optimizations (kernel parameters, memory management, network stack) - runs before service installation
- **Phase 11** (`service-optimization.sh`): Service-specific optimizations (PostgreSQL, PM2, NGINX) - runs after all services are installed

This approach eliminates configuration overwrite issues and ensures all optimizations are applied correctly.

## System Specifications

- **CPU**: 4 vCPUs (Intel/AMD x64)
- **RAM**: 8 GB
- **Swap**: 4 GB configured with swappiness=10
- **Storage**: SSD optimized settings
- **OS**: Ubuntu 24.04 LTS

## Optimization Components

### 1. PostgreSQL Database Optimization

#### Memory Configuration
- **shared_buffers**: 2GB (25% of total RAM)
  - Primary buffer cache for database pages
  - Optimal for 8GB system with mixed workloads
- **effective_cache_size**: 6GB (75% of total RAM)
  - Tells <PERSON>greS<PERSON> how much memory is available for caching
  - Includes both shared_buffers and OS cache
- **work_mem**: 16MB per operation
  - Memory for sorting, hash joins, and other operations
  - Calculated as RAM/(4*max_connections) = 8GB/(4*200) = 10MB, rounded up to 16MB
- **maintenance_work_mem**: 512MB
  - Memory for maintenance operations (VACUUM, CREATE INDEX)
  - Set to 1/16 of total RAM

#### Connection Management
- **max_connections**: 200
  - Supports high-concurrency trip operations
  - Balanced for 8GB RAM system
  - Each connection uses ~2-4MB of memory

#### Write-Ahead Logging (WAL)
- **wal_buffers**: 16MB (3% of shared_buffers)
  - Buffer for WAL data before writing to disk
- **max_wal_size**: 2GB
  - Maximum WAL size before checkpoint
  - Optimized for write-heavy trip workflow operations

#### Query Optimization
- **random_page_cost**: 1.1 (SSD optimized)
  - Lower than default 4.0 for SSD storage
- **effective_io_concurrency**: 200 (SSD optimized)
  - Number of concurrent I/O operations
- **checkpoint_completion_target**: 0.9
  - Spreads checkpoint I/O over 90% of checkpoint interval

#### Performance Monitoring
- **log_min_duration_statement**: 1000ms
  - Logs queries taking longer than 1 second
- **log_checkpoints**: on
- **log_connections**: on
- **log_lock_waits**: on

### 2. Node.js/PM2 Process Management

#### Cluster Configuration
- **instances**: 4 (one per vCPU)
  - Utilizes all available CPU cores
  - Provides load balancing and fault tolerance
- **exec_mode**: cluster
  - Enables PM2's cluster mode with load balancing
- **instance_var**: INSTANCE_ID
  - Unique identifier for each worker process

#### Memory Management
- **max_memory_restart**: 1500M per process
  - Prevents memory leaks from crashing the system
  - Safe limit: 4 * 1500M = 6GB total, leaving 2GB for system
- **node_args**: --max-old-space-size=1400
  - V8 heap size limit slightly below PM2 restart limit
  - Prevents out-of-memory errors

#### Process Monitoring
- **kill_timeout**: 5000ms
- **listen_timeout**: 10000ms
- **restart_delay**: 1000ms
- **max_restarts**: 10
- **min_uptime**: 10s

### 3. NGINX Web Server Optimization

#### Worker Configuration
- **worker_processes**: 4 (matches vCPU count)
  - One worker per CPU core for optimal performance
- **worker_connections**: 2048 per worker
  - Total: 4 * 2048 = 8192 concurrent connections
  - Optimal for 8GB RAM system

#### Buffer Settings
- **client_body_buffer_size**: 128k
  - Optimized for QR code image uploads
- **client_header_buffer_size**: 32k
  - Handles large headers from mobile devices
- **client_max_body_size**: 10M
  - Allows file uploads up to 10MB
- **large_client_header_buffers**: 4 32k

#### Compression
- **gzip_comp_level**: 6
  - Balanced compression for React frontend assets
  - Good compression ratio without excessive CPU usage
- **gzip_types**: Optimized for web assets
  - text/plain, text/css, text/javascript, application/javascript, etc.

#### Connection Management
- **keepalive_timeout**: 65s
  - Keeps connections alive for mobile clients
- **keepalive_requests**: 1000
  - Maximum requests per connection

#### Rate Limiting
- **API rate limit**: 30 requests/minute per IP
- **QR scanning rate limit**: 60 requests/minute per IP
- Prevents abuse and ensures fair resource usage

### 4. System-Level Optimizations

#### Memory Management
- **vm.swappiness**: 10
  - Uses swap only during memory pressure spikes
  - Keeps active data in RAM for better performance
- **vm.dirty_ratio**: 15
  - Maximum dirty pages before forced writeback
- **vm.dirty_background_ratio**: 5
  - Background writeback threshold
- **vm.overcommit_memory**: 2
  - Strict overcommit accounting
- **vm.overcommit_ratio**: 80
  - Allow 80% memory overcommit

#### File System
- **fs.file-max**: 2,097,152
  - Maximum number of file handles system-wide
- **User file limits**: 65,536 per user
  - Handles high-concurrency operations

#### Network Stack
- **net.core.somaxconn**: 65,535
  - Maximum listen queue size
- **net.ipv4.tcp_max_syn_backlog**: 65,535
  - SYN flood protection
- **TCP keepalive settings**: Optimized for mobile connections
- **TCP buffer sizes**: Increased for high-throughput operations

#### Security
- **kernel.dmesg_restrict**: 1
- **kernel.kptr_restrict**: 2
- Prevents information disclosure

## Performance Impact

### Expected Improvements

1. **Database Performance**
   - 30-50% improvement in query response times
   - Better handling of concurrent connections
   - Reduced I/O wait times

2. **Application Performance**
   - 4x processing capacity with cluster mode
   - Better memory utilization
   - Automatic failover between instances

3. **Web Server Performance**
   - Higher concurrent connection capacity
   - Better compression ratios
   - Reduced response times for static assets

4. **System Performance**
   - Reduced memory pressure
   - Better I/O performance
   - Improved network throughput

### Success Criteria

- Handle 50+ concurrent trip operations without memory pressure (RAM usage <80%)
- Maintain sub-200ms response times for QR code scanning operations
- Support 100+ simultaneous driver connections
- Prevent system crashes during peak usage scenarios
- PostgreSQL query performance improved by 30-50%

## Monitoring and Validation

### Automated Validation

The `validate-system-optimization.sh` script automatically checks:

1. **PostgreSQL Configuration**
   - Verifies all performance parameters are applied
   - Checks database connectivity
   - Monitors active connections

2. **PM2 Cluster Status**
   - Validates ecosystem configuration
   - Checks running instances
   - Monitors memory usage

3. **NGINX Configuration**
   - Tests configuration syntax
   - Verifies performance settings
   - Checks service status

4. **System Parameters**
   - Validates kernel parameters
   - Checks memory and CPU resources
   - Monitors file limits

### Performance Report

The validation script generates a comprehensive performance report including:
- System resource utilization
- Service configuration status
- Performance recommendations
- Monitoring suggestions

## Rollback Procedures

### Automatic Rollback

If optimization fails during deployment:
1. Configuration backup is automatically created
2. Original settings are restored on error
3. Services are restarted with original configuration

### Manual Rollback

```bash
# Restore from backup
sudo cp /var/backups/hauling-qr-optimization-*/postgresql.conf /etc/postgresql/*/main/
sudo cp /var/backups/hauling-qr-optimization-*/nginx.conf /etc/nginx/
sudo cp /var/backups/hauling-qr-optimization-*/sysctl.conf /etc/
sudo cp /var/backups/hauling-qr-optimization-*/limits.conf /etc/security/

# Restart services
sudo systemctl restart postgresql
sudo systemctl restart nginx
sudo sysctl -p
```

## Maintenance

### Regular Monitoring

1. **Database Performance**
   ```bash
   # Check slow queries
   sudo -u postgres psql -d hauling_qr_system -c "
   SELECT query, calls, total_time, mean_time 
   FROM pg_stat_statements 
   ORDER BY total_time DESC LIMIT 10;"
   ```

2. **PM2 Monitoring**
   ```bash
   # Check cluster status
   pm2 monit
   pm2 list
   ```

3. **System Resources**
   ```bash
   # Monitor resource usage
   htop
   free -h
   df -h
   ```

### Performance Tuning

Based on actual usage patterns, consider adjusting:
- PostgreSQL `work_mem` for complex queries
- PM2 `max_memory_restart` based on actual memory usage
- NGINX `worker_connections` based on concurrent load
- System parameters based on monitoring data

## Troubleshooting

### Common Issues

1. **PostgreSQL won't start after optimization**
   - Check configuration syntax: `sudo -u postgres postgres --check-config`
   - Review logs: `sudo tail -f /var/log/postgresql/postgresql-*.log`
   - Restore from backup if needed

2. **PM2 cluster not starting**
   - Verify ecosystem.config.js syntax
   - Check available memory
   - Review PM2 logs: `pm2 logs`

3. **NGINX configuration errors**
   - Test configuration: `sudo nginx -t`
   - Check error logs: `sudo tail -f /var/log/nginx/error.log`

4. **System performance issues**
   - Monitor resource usage: `htop`, `iotop`
   - Check kernel messages: `dmesg`
   - Review system logs: `journalctl -f`

## Integration with Deployment Pipeline

The optimization module integrates seamlessly with the enhanced deployment system:

1. **Phase 0**: System Resource Optimization (NEW)
2. **Phase 1**: System Dependencies Installation
3. **Phase 2**: Repository and Environment Setup
4. **Phase 3**: Application Building
5. **Phase 4**: Database Installation
6. **Phase 5**: Web Server Installation
7. **Phase 6**: Process Manager Installation
8. **Phase 7**: Permission Management
9. **Phase 8**: Cleanup Operations
10. **Phase 9**: System Optimization Validation (NEW)
11. **Phase 10**: Final Deployment Verification

The optimization runs before any services are installed, ensuring optimal configuration from the start.
