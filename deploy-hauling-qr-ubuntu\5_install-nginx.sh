#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - MODULAR NGINX INSTALLATION SCRIPT
# =============================================================================
# Version: 1.0.0 - Extracted from auto-deploy.sh
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Self-contained Nginx installation with WSL-compatible systemctl alternatives
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="install-nginx.sh"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/nginx-install-$(date +%Y%m%d-%H%M%S).log"

# Application Configuration
readonly APP_DIR="/var/www/hauling-qr-system"
readonly PRODUCTION_DOMAIN="truckhaul.top"
readonly SERVER_HTTP_PORT=8080  # Cloudflare-compatible port

# WSL Detection
readonly IS_WSL=$(grep -qi microsoft /proc/version && echo "true" || echo "false")

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================
cleanup_on_error() {
  local exit_code=$?
  if [[ $exit_code -ne 0 ]]; then
    log_error "Nginx installation failed with exit code $exit_code"
    log_error "Check log file: $LOG_FILE"
  fi
  exit $exit_code
}

trap cleanup_on_error ERR

# =============================================================================
# WSL-COMPATIBLE NGINX FUNCTIONS
# =============================================================================

# Fix nginx inherited sockets issue
fix_nginx_inherited_sockets() {
  log_info "🔧 Fixing nginx inherited sockets issue..."

  # Stop nginx cleanly to prevent inherited file descriptors
  log_info "Stopping nginx cleanly to clear inherited sockets..."
  sudo systemctl stop nginx 2>/dev/null || true
  sudo service nginx stop 2>/dev/null || true
  sudo /etc/init.d/nginx stop 2>/dev/null || true
  sudo pkill -f nginx 2>/dev/null || true

  # Clear any inherited file descriptors and port conflicts
  log_info "Clearing inherited file descriptors and port conflicts..."
  sudo fuser -k 80/tcp 2>/dev/null || true
  sudo fuser -k 443/tcp 2>/dev/null || true

  # Remove any nginx PID files that might cause issues
  sudo rm -f /var/run/nginx.pid 2>/dev/null || true
  sudo rm -f /run/nginx.pid 2>/dev/null || true

  # Wait for complete cleanup
  sleep 3

  log_success "✅ Nginx inherited sockets cleanup completed"
  return 0
}

# Verify nginx is running without inherited sockets warnings
verify_nginx_clean_start() {
  log_info "🔍 Verifying nginx started cleanly without inherited sockets..."

  # Check if nginx is running
  if ! pgrep nginx >/dev/null 2>&1; then
    log_warning "⚠️ Nginx is not running"
    return 1
  fi

  # Check nginx error log for inherited sockets warnings (last 10 lines)
  local error_log="/var/log/nginx/error.log"
  if [[ -f "$error_log" ]]; then
    local inherited_sockets=$(tail -10 "$error_log" 2>/dev/null | grep -i "inherited sockets" | tail -1)
    if [[ -n "$inherited_sockets" ]]; then
      log_info "📝 Last inherited sockets message: $inherited_sockets"
      log_info "✅ This is normal and indicates nginx is reusing connections properly"
    else
      log_success "✅ No recent inherited sockets messages found"
    fi
  else
    log_info "📝 Nginx error log not found or not accessible"
  fi

  # Test nginx configuration
  if sudo nginx -t >/dev/null 2>&1; then
    log_success "✅ Nginx configuration is valid"
  else
    log_warning "⚠️ Nginx configuration has issues"
    return 1
  fi

  log_success "✅ Nginx is running cleanly"
  return 0
}
wsl_compatible_nginx_start() {
  log_info "🔄 Starting Nginx with WSL-compatible methods..."

  # First, fix inherited sockets issue
  fix_nginx_inherited_sockets

  local attempt=1
  local max_attempts=5
  local success=false

  while [[ $attempt -le $max_attempts ]] && [[ "$success" == "false" ]]; do
    log_info "Nginx start attempt $attempt/$max_attempts..."

    case $attempt in
      1)
        log_info "Method 1: Direct nginx binary start (clean)"
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
      2)
        log_info "Method 2: Service nginx start (clean)"
        if timeout 10 sudo service nginx start >/dev/null 2>&1; then
          success=true
        fi
        ;;
      3)
        log_info "Method 3: Init.d nginx start (clean)"
        if timeout 10 sudo /etc/init.d/nginx start >/dev/null 2>&1; then
          success=true
        fi
        ;;
      4)
        log_info "Method 4: Systemctl nginx start (clean)"
        if timeout 10 sudo systemctl start nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
      5)
        log_info "Method 5: Force cleanup and direct start"
        # Additional cleanup for stubborn cases
        sudo pkill -9 -f nginx 2>/dev/null || true
        sudo fuser -k 80/tcp 2>/dev/null || true
        sudo fuser -k 443/tcp 2>/dev/null || true
        sudo rm -f /var/run/nginx.pid /run/nginx.pid 2>/dev/null || true
        sleep 3
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
    esac

    # Check if nginx is actually running
    if [[ "$success" == "true" ]]; then
      sleep 2
      if pgrep nginx >/dev/null 2>&1; then
        log_success "✅ Nginx started successfully using method $attempt"
        return 0
      else
        log_warning "⚠️ Nginx start command succeeded but process not found, trying next method..."
        success=false
      fi
    fi

    attempt=$((attempt + 1))
    if [[ $attempt -le $max_attempts ]]; then
      sleep 3
    fi
  done

  log_error "❌ All Nginx start methods failed"
  return 1
}

wsl_compatible_nginx_reload() {
  log_info "🔄 Reloading Nginx configuration with WSL-compatible methods..."
  
  # First check if nginx is running
  if ! pgrep nginx >/dev/null 2>&1; then
    log_warning "⚠️ Nginx not running, starting instead of reloading..."
    return wsl_compatible_nginx_start
  fi

  local attempt=1
  local max_attempts=5
  local success=false

  while [[ $attempt -le $max_attempts ]] && [[ "$success" == "false" ]]; do
    log_info "Nginx reload attempt $attempt/$max_attempts..."

    case $attempt in
      1)
        log_info "Method 1: Direct nginx -s reload"
        if timeout 10 sudo nginx -s reload >/dev/null 2>&1; then
          success=true
        fi
        ;;
      2)
        log_info "Method 2: Service nginx reload"
        if timeout 10 sudo service nginx reload >/dev/null 2>&1; then
          success=true
        fi
        ;;
      3)
        log_info "Method 3: Service nginx restart"
        if timeout 10 sudo service nginx restart >/dev/null 2>&1; then
          success=true
        fi
        ;;
      4)
        log_info "Method 4: Init.d nginx restart"
        if timeout 10 sudo /etc/init.d/nginx restart >/dev/null 2>&1; then
          success=true
        fi
        ;;
      5)
        log_info "Method 5: Clean restart with inherited sockets fix"
        fix_nginx_inherited_sockets
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
    esac

    if [[ "$success" == "true" ]]; then
      sleep 2
      if pgrep nginx >/dev/null 2>&1; then
        log_success "✅ Nginx reloaded successfully using method $attempt"
        return 0
      else
        log_warning "⚠️ Nginx reload command succeeded but process not found, trying next method..."
        success=false
      fi
    fi

    attempt=$((attempt + 1))
    if [[ $attempt -le $max_attempts ]]; then
      sleep 2
    fi
  done

  log_error "❌ All Nginx reload methods failed"
  return 1
}

install_nginx() {
  log_info "📦 Installing Nginx web server..."

  # Check if Nginx is already installed
  if command -v nginx >/dev/null 2>&1; then
    log_info "Nginx already installed: $(nginx -v 2>&1)"
    return 0
  fi

  # Install Nginx
  log_info "Installing Nginx package..."
  if ! sudo apt-get install -y nginx >>"$LOG_FILE" 2>&1; then
    log_error "Failed to install Nginx"
    return 1
  fi

  # Enable Nginx (if systemctl is available)
  if command -v systemctl >/dev/null 2>&1; then
    sudo systemctl enable nginx >/dev/null 2>&1 || true
  fi

  log_success "✅ Nginx installed successfully"
  return 0
}

configure_nginx_site() {
  log_info "🔧 Configuring Nginx site for Hauling QR Trip System..."

  # Note: NGINX performance optimizations will be applied in Phase 11 (11_service-optimization.sh)

  # Detect VPS IP for configuration
  local DETECTED_VPS_IP
  DETECTED_VPS_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")

  # Load domain from .env if present
  local domain="${PRODUCTION_DOMAIN}"
  if [[ -f "$APP_DIR/.env" ]]; then
    domain=$(grep -E '^PRODUCTION_DOMAIN=' "$APP_DIR/.env" | sed -E 's/PRODUCTION_DOMAIN=\"?([^\"]*)\"?/\1/' || echo "${PRODUCTION_DOMAIN}")
  fi

  log_info "Configuring Nginx for domain: $domain (IP: $DETECTED_VPS_IP)"

  # Create Nginx site configuration (IDEMPOTENT: overwrites existing file completely)
  sudo tee /etc/nginx/sites-available/hauling-qr-system >/dev/null <<EOF
server {
    listen 80;
    server_name ${domain} www.${domain} api.${domain} ${DETECTED_VPS_IP};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers with WebSocket and WebAssembly support
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.truckhaul.top wss://api.truckhaul.top *.cloudflare.com *.cloudflareinsights.com;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    # Ensure proper MIME types for static assets with CORS
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${domain}";
    }

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path - serve from client/public/images
    location /images/ {
        alias /var/www/hauling-qr-system/client/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${domain}";
        try_files \$uri =404;
    }

    # API - NGINX-ONLY CORS configuration (Express.js disabled)
    location /api {
        # Handle preflight OPTIONS requests FIRST
        if (\$request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://${domain}' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token, DNT, User-Agent, If-Modified-Since, Range' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # CORS headers for actual requests (GET, POST, etc.)
        add_header 'Access-Control-Allow-Origin' 'https://${domain}' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
        add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token' always;
        add_header 'Access-Control-Expose-Headers' 'Authorization, Content-Length, X-Requested-With' always;

        proxy_pass http://localhost:${SERVER_HTTP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https; # indicate HTTPS via Cloudflare
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # WebSocket support - Using Cloudflare compatible HTTPS port
    location /ws {
        proxy_pass http://localhost:${SERVER_HTTP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # Socket.IO WebSocket support - Using Cloudflare compatible HTTPS port
    location /socket.io/ {
        proxy_pass http://localhost:${SERVER_HTTP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }


}
EOF

  # Enable the site and disable default
  sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
  sudo rm -f /etc/nginx/sites-enabled/default || true

  log_success "✅ Nginx site configuration created"
  return 0
}

test_and_apply_nginx_config() {
  log_info "🧪 Testing Nginx configuration..."

  # Test Nginx configuration
  if ! sudo nginx -t >/dev/null 2>&1; then
    log_error "❌ Nginx configuration test failed"
    sudo nginx -t 2>&1 | head -10 | tee -a "$LOG_FILE"
    return 1
  fi

  log_success "✅ Nginx configuration test passed"

  # Apply configuration using WSL-compatible methods
  if ! wsl_compatible_nginx_reload; then
    log_warning "⚠️ Nginx reload failed, attempting fresh start..."
    if ! wsl_compatible_nginx_start; then
      log_error "❌ Failed to start Nginx with new configuration"
      return 1
    fi
  fi

  log_success "✅ Nginx configuration applied successfully"
  return 0
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  log_info "🚀 Starting Nginx installation for Hauling QR Trip System..."
  log_info "Script: $SCRIPT_NAME"
  log_info "Log file: $LOG_FILE"
  log_info "WSL Environment: $IS_WSL"

  # Step 1: Install Nginx
  if ! install_nginx; then
    log_error "❌ Nginx installation failed"
    exit 1
  fi

  # Step 2: Configure Nginx site
  if ! configure_nginx_site; then
    log_error "❌ Nginx site configuration failed"
    exit 1
  fi

  # Step 3: Test and apply configuration
  if ! test_and_apply_nginx_config; then
    log_error "❌ Nginx configuration application failed"
    exit 1
  fi

  # Step 4: Verify clean nginx startup (no inherited sockets issues)
  if ! verify_nginx_clean_start; then
    log_warning "⚠️ Nginx verification had issues, but installation completed"
  fi

  log_success "✅ Nginx installation and configuration completed successfully"
  log_info "📋 Installation Summary:"
  log_info "   - Nginx installed and configured"
  log_info "   - Site configuration for ${PRODUCTION_DOMAIN} created"
  log_info "   - Cloudflare SSL termination configured"
  log_info "   - WebSocket and API proxy configured"
  log_info "   - WSL-compatible service management used"
  log_info "   - Inherited sockets issue prevention implemented"
  log_info "   - Clean nginx startup verified"

  return 0
}

# Execute main function
main "$@"
