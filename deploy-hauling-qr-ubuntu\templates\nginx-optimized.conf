# =============================================================================
# HAULING QR TRIP SYSTEM - OPTIMIZED NGINX CONFIGURATION
# =============================================================================
# System: 4 vCPU / 8GB RAM VPS
# Optimized for high-concurrency operations and mobile access
# Applied via optimize-system-resources.sh
# =============================================================================

user www-data;
worker_processes 4;                      # One worker per vCPU
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

# Worker Configuration (optimized for 4 vCPU / 8GB RAM)
events {
    worker_connections 2048;             # 2048 per worker = 8192 total
    use epoll;                           # Linux-optimized event method
    multi_accept on;                     # Accept multiple connections at once
    accept_mutex off;                    # Disable accept mutex for better performance
}

http {
    # ==========================================================================
    # BASIC SETTINGS
    # ==========================================================================
    
    sendfile on;                         # Use sendfile for static files
    tcp_nopush on;                       # Optimize packet sending
    tcp_nodelay on;                      # Disable Nagle's algorithm
    keepalive_timeout 65s;               # Keep connections alive for mobile clients
    keepalive_requests 1000;             # Maximum requests per connection
    types_hash_max_size 2048;            # Hash table size for MIME types
    server_tokens off;                   # Hide NGINX version
    
    # File Upload Settings (optimized for QR code uploads)
    client_max_body_size 10M;            # Maximum upload size
    client_body_buffer_size 128k;        # Buffer for request body
    client_header_buffer_size 32k;       # Buffer for request headers
    large_client_header_buffers 4 32k;   # Large header buffers
    client_body_timeout 60s;             # Timeout for reading request body
    client_header_timeout 60s;           # Timeout for reading request headers
    send_timeout 60s;                    # Timeout for sending response
    
    # MIME Types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # ==========================================================================
    # COMPRESSION SETTINGS (optimized for React frontend)
    # ==========================================================================
    
    gzip on;                             # Enable gzip compression
    gzip_vary on;                        # Add Vary: Accept-Encoding header
    gzip_proxied any;                    # Compress for all proxied requests
    gzip_comp_level 6;                   # Balanced compression level
    gzip_min_length 1000;                # Minimum file size to compress
    gzip_disable "msie6";                # Disable for IE6
    
    # File types to compress
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/json
        application/javascript
        application/json
        application/xml
        application/xml+rss
        application/atom+xml
        image/svg+xml
        font/truetype
        font/opentype
        application/font-woff
        application/font-woff2;
    
    # ==========================================================================
    # RATE LIMITING (prevent API abuse)
    # ==========================================================================
    
    # Define rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;      # API endpoints
    limit_req_zone $binary_remote_addr zone=qr:10m rate=60r/m;       # QR scanning
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;     # Authentication
    limit_req_zone $binary_remote_addr zone=upload:10m rate=20r/m;   # File uploads
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;              # Per IP connections
    limit_conn_zone $server_name zone=perserver:10m;                 # Per server connections
    
    # ==========================================================================
    # CACHING SETTINGS
    # ==========================================================================
    
    # Proxy cache path (if using reverse proxy caching)
    proxy_cache_path /var/cache/nginx/hauling-qr 
                     levels=1:2 
                     keys_zone=hauling_cache:10m 
                     max_size=1g 
                     inactive=60m 
                     use_temp_path=off;
    
    # Static file caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg)$ {
        expires 1y;                      # Cache static assets for 1 year
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;                  # Don't log static file requests
    }
    
    # ==========================================================================
    # LOGGING CONFIGURATION
    # ==========================================================================
    
    # Custom log format with performance metrics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    # Performance-focused log format
    log_format performance '$time_local $remote_addr $request_method $uri '
                          '$status $body_bytes_sent $request_time '
                          '$upstream_response_time "$http_user_agent"';
    
    # Error log format
    error_log /var/log/nginx/error.log warn;
    access_log /var/log/nginx/access.log main;
    
    # ==========================================================================
    # SSL/TLS SETTINGS (for future HTTPS implementation)
    # ==========================================================================
    
    # SSL protocols and ciphers (commented out for HTTP-only deployment)
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    # ssl_prefer_server_ciphers off;
    # ssl_session_cache shared:SSL:10m;
    # ssl_session_timeout 10m;
    
    # ==========================================================================
    # UPSTREAM CONFIGURATION (PM2 cluster backend)
    # ==========================================================================
    
    upstream hauling_backend {
        # PM2 cluster instances (all on port 8080 with load balancing)
        server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
        
        # Load balancing method
        least_conn;                      # Route to least connected server
        
        # Keep alive connections to backend
        keepalive 32;                    # Keep 32 connections alive
        keepalive_requests 100;          # Requests per keepalive connection
        keepalive_timeout 60s;           # Keepalive timeout
    }
    
    # ==========================================================================
    # SECURITY HEADERS
    # ==========================================================================
    
    # Security headers (applied to all responses)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Content Security Policy (adjust based on application needs)
    # add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
    
    # ==========================================================================
    # SERVER CONFIGURATION
    # ==========================================================================
    
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        
        server_name truckhaul.top www.truckhaul.top _;
        root /var/www/hauling-qr-system/client/build;
        index index.html index.htm;
        
        # Connection limits
        limit_conn perip 20;             # 20 connections per IP
        limit_conn perserver 1000;       # 1000 connections per server
        
        # =======================================================================
        # API ENDPOINTS (proxy to Node.js backend)
        # =======================================================================
        
        # General API endpoints
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=10 nodelay;
            
            # Proxy settings
            proxy_pass http://hauling_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }
        
        # QR scanning endpoints (higher rate limit)
        location /api/qr/ {
            limit_req zone=qr burst=20 nodelay;
            
            proxy_pass http://hauling_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Faster timeouts for QR operations
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Authentication endpoints (stricter rate limiting)
        location /api/auth/ {
            limit_req zone=auth burst=5 nodelay;
            
            proxy_pass http://hauling_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Standard timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # File upload endpoints
        location /api/upload/ {
            limit_req zone=upload burst=10 nodelay;
            
            # Increase limits for file uploads
            client_max_body_size 10M;
            client_body_timeout 120s;
            
            proxy_pass http://hauling_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for uploads
            proxy_connect_timeout 30s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
            
            # Disable buffering for uploads
            proxy_request_buffering off;
        }
        
        # WebSocket endpoints
        location /ws {
            proxy_pass http://hauling_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket-specific settings
            proxy_read_timeout 86400s;   # 24 hours for long-lived connections
            proxy_send_timeout 86400s;
        }
        
        # =======================================================================
        # STATIC FILE SERVING (React frontend)
        # =======================================================================
        
        # Static assets with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
            
            # Try files, fallback to index.html for SPA
            try_files $uri $uri/ /index.html;
        }
        
        # React Router (SPA) - all routes serve index.html
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache control for HTML files
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Error pages
        error_page 404 /index.html;      # SPA handles 404s
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /var/www/html;
        }
    }
    
    # ==========================================================================
    # ADDITIONAL SERVER BLOCKS (if needed)
    # ==========================================================================
    
    # Redirect www to non-www (optional)
    server {
        listen 80;
        server_name www.truckhaul.top;
        return 301 http://truckhaul.top$request_uri;
    }
    
    # Include additional site configurations
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}

# =============================================================================
# PERFORMANCE NOTES
# =============================================================================
#
# 1. Worker Configuration:
#    - 4 workers × 2048 connections = 8192 total concurrent connections
#    - Optimal for 4 vCPU system with high-concurrency requirements
#
# 2. Memory Usage:
#    - Each worker uses ~10-20MB base memory
#    - Total NGINX memory: ~100-200MB
#    - Leaves plenty of RAM for applications
#
# 3. Rate Limiting:
#    - API: 30 requests/minute (normal operations)
#    - QR: 60 requests/minute (scanning operations)
#    - Auth: 10 requests/minute (security)
#    - Upload: 20 requests/minute (file operations)
#
# 4. Caching Strategy:
#    - Static assets: 1 year cache
#    - HTML files: No cache (SPA updates)
#    - API responses: No cache (dynamic data)
#
# 5. Compression:
#    - Level 6 provides good compression ratio
#    - Reduces bandwidth usage by 60-80%
#    - Minimal CPU overhead on modern systems
#
# 6. Security:
#    - Rate limiting prevents abuse
#    - Security headers protect against common attacks
#    - File access restrictions prevent information disclosure
#
# =============================================================================
