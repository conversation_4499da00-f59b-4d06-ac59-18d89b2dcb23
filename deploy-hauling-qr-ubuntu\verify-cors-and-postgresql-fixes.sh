#!/bin/bash

# =============================================================================
# COMPREHENSIVE VERIFICATION SCRIPT - CORS & POSTGRESQL FIXES
# =============================================================================
# Version: 1.0.0
# Description: Verify CORS environment variables and PostgreSQL timeout optimization
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# =============================================================================

set -euo pipefail

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO] $1${NC}"
}

log_success() {
  echo -e "${GREEN}[SUCCESS] $1${NC}"
}

log_warning() {
  echo -e "${YELLOW}[WARNING] $1${NC}"
}

log_error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

log_header() {
  echo -e "${CYAN}=============================================================================="
  echo -e "$1"
  echo -e "==============================================================================${NC}"
}

# =============================================================================
# CORS ENVIRONMENT VARIABLE VERIFICATION
# =============================================================================
verify_cors_environment_variables() {
  log_header "🔍 CORS ENVIRONMENT VARIABLE VERIFICATION"
  
  local app_dir="/var/www/hauling-qr-system"
  local local_app_dir="."
  local verification_passed=true
  
  # Determine which directory to check
  if [[ -d "$app_dir" ]]; then
    local check_dir="$app_dir"
    log_info "📁 Checking production directory: $app_dir"
  elif [[ -f "$local_app_dir/.env" || -f "$local_app_dir/.env.dev" || -f "$local_app_dir/.env.prod" ]]; then
    local check_dir="$local_app_dir"
    log_info "📁 Checking local directory: $local_app_dir"
  else
    log_error "❌ No environment files found in either production or local directory"
    return 1
  fi
  
  # Required CORS variables
  local required_vars=("NGINX_PROXY_MODE" "EXPRESS_CORS_DISABLED" "CORS_HANDLED_BY_NGINX")
  local env_files=(".env" ".env.dev" ".env.prod")
  
  for env_file in "${env_files[@]}"; do
    local file_path="$check_dir/$env_file"
    
    if [[ -f "$file_path" ]]; then
      log_info "🔍 Checking $env_file..."
      
      for var in "${required_vars[@]}"; do
        if grep -q "^${var}=true" "$file_path" 2>/dev/null; then
          log_success "  ✅ $var=true found in $env_file"
        else
          log_error "  ❌ $var=true MISSING in $env_file"
          verification_passed=false
        fi
      done
      
      # Check for deployment marker
      if grep -q "HAULING-QR-DEPLOYMENT-CORS-DISABLE" "$file_path" 2>/dev/null; then
        log_success "  ✅ Deployment marker found in $env_file"
      else
        log_warning "  ⚠️ Deployment marker not found in $env_file (may be added during deployment)"
      fi
      
    else
      log_warning "⚠️ $env_file not found in $check_dir"
    fi
    echo ""
  done
  
  # Active environment file check
  local active_env="$check_dir/.env"
  if [[ -f "$active_env" ]]; then
    log_info "🎯 CRITICAL: Active .env file verification"
    local active_env_passed=true
    
    for var in "${required_vars[@]}"; do
      if grep -q "^${var}=true" "$active_env" 2>/dev/null; then
        log_success "  ✅ ACTIVE: $var=true found in .env"
      else
        log_error "  ❌ CRITICAL: $var=true MISSING in active .env file"
        active_env_passed=false
        verification_passed=false
      fi
    done
    
    if [[ "$active_env_passed" == true ]]; then
      log_success "🎉 CRITICAL SUCCESS: All CORS variables present in active .env file"
    else
      log_error "💥 CRITICAL FAILURE: CORS variables missing from active .env file"
    fi
  else
    log_error "💥 CRITICAL: Active .env file not found"
    verification_passed=false
  fi
  
  return $([[ "$verification_passed" == true ]] && echo 0 || echo 1)
}

# =============================================================================
# POSTGRESQL TIMEOUT VERIFICATION
# =============================================================================
verify_postgresql_timeout() {
  log_header "⏱️ POSTGRESQL TIMEOUT VERIFICATION"
  
  local script_path="deploy-hauling-qr-ubuntu/4_install-postgresql.sh"
  local verification_passed=true
  
  if [[ -f "$script_path" ]]; then
    log_info "📄 Checking PostgreSQL installation script: $script_path"
    
    # Check timeout value
    local timeout_line=$(grep "POSTGRES_INSTALL_TIMEOUT=" "$script_path" | head -1)
    if [[ -n "$timeout_line" ]]; then
      log_info "🔍 Found timeout configuration: $timeout_line"
      
      if echo "$timeout_line" | grep -q "POSTGRES_INSTALL_TIMEOUT=300"; then
        log_success "✅ PostgreSQL timeout correctly set to 300 seconds (5 minutes)"
      else
        log_error "❌ PostgreSQL timeout not set to 300 seconds"
        verification_passed=false
      fi
    else
      log_error "❌ PostgreSQL timeout configuration not found"
      verification_passed=false
    fi
    
    # Check comment consistency
    if grep -q "# 5 minutes maximum" "$script_path"; then
      log_success "✅ Comment correctly updated to '5 minutes maximum'"
    else
      log_warning "⚠️ Comment may not be updated to reflect 5-minute timeout"
    fi
    
    # Check target time references
    local target_300_count=$(grep -c "target: <300s" "$script_path" 2>/dev/null || echo "0")
    if [[ "$target_300_count" -ge 2 ]]; then
      log_success "✅ Target time references updated to 300s ($target_300_count instances)"
    else
      log_warning "⚠️ Target time references may not be fully updated (found $target_300_count instances)"
    fi
    
    # Check log message consistency
    if grep -q "Complete within 5 minutes" "$script_path"; then
      log_success "✅ Log message updated to '5 minutes' target"
    else
      log_warning "⚠️ Log message may not reflect 5-minute target"
    fi
    
  else
    log_error "❌ PostgreSQL installation script not found: $script_path"
    verification_passed=false
  fi
  
  return $([[ "$verification_passed" == true ]] && echo 0 || echo 1)
}

# =============================================================================
# DEPLOYMENT READINESS CHECK
# =============================================================================
verify_deployment_readiness() {
  log_header "🚀 DEPLOYMENT READINESS CHECK"
  
  local readiness_passed=true
  
  # Check deployment scripts exist
  local required_scripts=(
    "deploy-hauling-qr-ubuntu/auto-deploy.sh"
    "deploy-hauling-qr-ubuntu/2_setup-repository-environment.sh"
    "deploy-hauling-qr-ubuntu/4_install-postgresql.sh"
    "deploy-hauling-qr-ubuntu/5_install-nginx.sh"
    "deploy-hauling-qr-ubuntu/test-cors-fix.sh"
  )
  
  for script in "${required_scripts[@]}"; do
    if [[ -f "$script" ]]; then
      log_success "✅ $script exists"
    else
      log_error "❌ $script missing"
      readiness_passed=false
    fi
  done
  
  # Check deployment markers script
  if [[ -f "deploy-hauling-qr-ubuntu/deployment-markers.sh" ]]; then
    log_success "✅ Deployment markers system available"
  else
    log_warning "⚠️ Deployment markers system not found (optional)"
  fi
  
  return $([[ "$readiness_passed" == true ]] && echo 0 || echo 1)
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  log_header "🎯 COMPREHENSIVE VERIFICATION - CORS & POSTGRESQL FIXES"
  echo ""
  
  local overall_success=true
  
  # Run CORS verification
  if verify_cors_environment_variables; then
    log_success "🎉 CORS Environment Variables: PASSED"
  else
    log_error "💥 CORS Environment Variables: FAILED"
    overall_success=false
  fi
  echo ""
  
  # Run PostgreSQL timeout verification
  if verify_postgresql_timeout; then
    log_success "🎉 PostgreSQL Timeout Optimization: PASSED"
  else
    log_error "💥 PostgreSQL Timeout Optimization: FAILED"
    overall_success=false
  fi
  echo ""
  
  # Run deployment readiness check
  if verify_deployment_readiness; then
    log_success "🎉 Deployment Readiness: PASSED"
  else
    log_error "💥 Deployment Readiness: FAILED"
    overall_success=false
  fi
  echo ""
  
  # Final result
  if [[ "$overall_success" == true ]]; then
    log_header "🎉 VERIFICATION COMPLETE: ALL CHECKS PASSED"
    log_success "✅ CORS duplicate header fix is properly configured"
    log_success "✅ PostgreSQL installation timeout optimized to 5 minutes"
    log_success "✅ System ready for production deployment"
    echo ""
    log_info "🚀 Next steps:"
    log_info "   1. Run: ./deploy-hauling-qr-ubuntu/auto-deploy.sh"
    log_info "   2. Test: ./deploy-hauling-qr-ubuntu/test-cors-fix.sh truckhaul.top"
    log_info "   3. Verify: Login functionality works without CORS errors"
    return 0
  else
    log_header "💥 VERIFICATION FAILED: ISSUES DETECTED"
    log_error "❌ One or more critical issues found"
    log_error "❌ Review the errors above and fix before deployment"
    return 1
  fi
}

# Execute main function
main "$@"
