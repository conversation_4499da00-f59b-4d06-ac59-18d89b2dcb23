#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - APPLICATION BUILD MODULE
# =============================================================================
# Version: 1.0.0 - Complete application building and dependency management
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Node.js dependencies, application building, and production patches
# =============================================================================

# Source shared configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh"

# Module-specific configuration
readonly MODULE_NAME="build-application"
readonly LOG_FILE="${LOG_DIR}/build-application-$(date +%Y%m%d-%H%M%S).log"

# Setup error handling
setup_error_handling "$MODULE_NAME"

# =============================================================================
# NODE.JS DEPENDENCIES VERIFICATION AND INSTALLATION
# =============================================================================
verify_and_install_node_dependencies() {
  log_info "🔍 Verifying and installing Node.js dependencies..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Verify Node.js and npm are available
  if ! command_exists node; then
    log_error "❌ Node.js not found. Please run install-system-dependencies.sh first"
    return 1
  fi
  
  if ! command_exists npm; then
    log_error "❌ npm not found. Please run install-system-dependencies.sh first"
    return 1
  fi
  
  log_info "Node.js version: $(node --version)"
  log_info "npm version: $(npm --version)"

  # Clean install to avoid conflicts
  log_info "Cleaning npm cache and node_modules..."
  npm cache clean --force 2>/dev/null || true
  rm -rf node_modules package-lock.json 2>/dev/null || true

  # Fix npm cache permissions first
  log_info "🔧 Fixing npm cache permissions..."
  sudo chown -R ubuntu:ubuntu ~/.npm 2>/dev/null || true

  # Install all dependencies with timeout and error handling
  log_info "Installing all Node.js dependencies with timeout protection..."

  # Set npm timeout and registry for better reliability
  npm config set fetch-timeout 60000
  npm config set fetch-retry-mintimeout 10000
  npm config set fetch-retry-maxtimeout 60000
  npm config set registry https://registry.npmjs.org/

  # Try npm install with timeout (5 minutes)
  if timeout 300 npm install --no-audit --no-fund 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ npm install completed successfully"
  else
    log_warning "⚠️ npm install failed or timed out, trying alternative methods..."

    # Clear npm cache and try again
    npm cache clean --force 2>/dev/null || true
    rm -rf node_modules package-lock.json 2>/dev/null || true

    # Try with --force and shorter timeout (3 minutes)
    if timeout 180 npm install --force --no-audit --no-fund 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ npm install with --force completed"
    else
      log_error "❌ npm install failed completely after timeout"
      return 1
    fi
  fi

  # Force install critical modules with better error handling
  local critical_modules=("pg" "express" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")
  for module in "${critical_modules[@]}"; do
    log_info "Verifying critical module: $module"
    if ! node -e "require('$module')" 2>/dev/null; then
      log_info "Installing missing module: $module"
      npm install "$module" --save 2>&1 | tee -a "$LOG_FILE" || {
        log_warning "⚠️ Failed to install $module, trying with --force..."
        npm install "$module" --save --force 2>&1 | tee -a "$LOG_FILE" || true
      }
    fi
  done

  log_success "✅ Node.js dependencies verification completed"
}

# =============================================================================
# PRODUCTION PATCHES
# =============================================================================
apply_production_patches() {
  log_info "🔧 Applying production patches..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Apply server-side patches
  if [[ -d "server" ]]; then
    log_info "Applying server production patches..."
    
    # Fix WSL server binding issues
    local server_js="server/server.js"
    if [[ -f "$server_js" ]]; then
      # Ensure server binds to 0.0.0.0 for WSL compatibility
      if grep -q "app.listen.*localhost" "$server_js"; then
        log_info "Fixing server binding for WSL compatibility..."
        sed -i 's/app\.listen.*localhost/app.listen(PORT, "0.0.0.0"/g' "$server_js"
        log_success "✅ Server binding fixed for WSL"
      fi
      
      # Fix JSON parsing issues in WSL
      if ! grep -q "app.use(express.json" "$server_js"; then
        log_info "Adding JSON parsing middleware..."
        sed -i '/const app = express();/a app.use(express.json({ limit: "50mb" }));' "$server_js"
        log_success "✅ JSON parsing middleware added"
      fi
    fi
  fi
  
  # Apply client-side patches
  if [[ -d "client" ]]; then
    log_info "Applying client production patches..."
    
    # Fix package.json for production build
    local client_package="client/package.json"
    if [[ -f "$client_package" ]]; then
      # Ensure proper build script
      if ! grep -q '"build".*"react-scripts build"' "$client_package"; then
        log_info "Fixing client build script..."
        sed -i 's/"build".*:.*".*"/"build": "react-scripts build"/g' "$client_package"
        log_success "✅ Client build script fixed"
      fi
    fi
  fi
  
  log_success "✅ Production patches applied"
}

# =============================================================================
# FRONTEND BUILD FIXES
# =============================================================================
fix_frontend_build_issues() {
  log_info "🔧 Fixing frontend build issues..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  if [[ ! -d "client" ]]; then
    log_warning "⚠️ Client directory not found, skipping frontend fixes"
    return 0
  fi
  
  cd "client" || {
    log_error "❌ Cannot access client directory"
    return 1
  }
  
  # Fix common React build issues
  log_info "Fixing React build configuration..."
  
  # Create or update .env file for client with production configuration
  log_info "Creating client .env file with production configuration..."

  # Check if parent .env exists and extract API URLs from it
  local api_url="http://localhost:${SERVER_HTTP_PORT}/api"
  local ws_url="ws://localhost:${SERVER_HTTP_PORT}"

  if [[ -f "../.env" ]]; then
    # Extract API URLs from parent .env file (which was copied from .env.prod)
    local parent_api_url=$(grep "^REACT_APP_API_URL=" "../.env" | cut -d'=' -f2- | tr -d '"')
    local parent_ws_url=$(grep "^REACT_APP_WS_URL=" "../.env" | cut -d'=' -f2- | tr -d '"')

    if [[ -n "$parent_api_url" ]]; then
      api_url="$parent_api_url"
      log_info "Using API URL from parent .env: $api_url"
    fi

    if [[ -n "$parent_ws_url" ]]; then
      ws_url="$parent_ws_url"
      log_info "Using WS URL from parent .env: $ws_url"
    fi
  elif [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
    # Fallback to production URLs if parent .env not found
    api_url="${PRODUCTION_API_URL}"
    ws_url="${PRODUCTION_WS_URL}"
    log_info "Using fallback production URLs"
  fi

  cat > .env << EOF
# React Client Environment - Generated for ${DEPLOYMENT_ENV}
GENERATE_SOURCEMAP=false
DISABLE_ESLINT_PLUGIN=true
SKIP_PREFLIGHT_CHECK=true

# API Configuration - FIXED for Cloudflare
REACT_APP_API_URL=${api_url}
REACT_APP_WS_URL=${ws_url}
REACT_APP_USE_HTTPS=false
EOF
  log_success "✅ Client .env file created with ${DEPLOYMENT_ENV} configuration"
  log_info "   API URL: ${api_url}"
  log_info "   WS URL: ${ws_url}"
  
  # Fix memory issues for large builds
  local package_json="package.json"
  if [[ -f "$package_json" ]]; then
    # Add memory optimization to build script
    if ! grep -q "max_old_space_size" "$package_json"; then
      log_info "Adding memory optimization to build script..."
      sed -i 's/"build": "react-scripts build"/"build": "node --max_old_space_size=4096 node_modules\/.bin\/react-scripts build"/g' "$package_json"
      log_success "✅ Memory optimization added to build script"
    fi
  fi
  
  cd "$APP_DIR"
  log_success "✅ Frontend build issues fixed"
}

# =============================================================================
# APPLICATION BUILDING
# =============================================================================
build_application() {
  log_info "🏗️ Building application components..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Step 1: Install root dependencies (for migration runner) with timeout
  log_info "Installing root dependencies (for migration runner)..."
  if ! timeout 180 npm install --no-audit --no-fund >>"$LOG_FILE" 2>&1; then
    log_warning "⚠️ Root npm install failed or timed out, trying with --force..."
    timeout 120 npm install --force --no-audit --no-fund >>"$LOG_FILE" 2>&1 || true
  fi

  # Apply security fixes for root dependencies
  log_info "🔒 Applying root security fixes..."
  npm audit fix >>"$LOG_FILE" 2>&1 || true

  # Step 2: Install server dependencies
  if [[ -d "server" ]]; then
    log_info "Installing server dependencies..."
    pushd "server" >/dev/null
    
    # Clean install for server
    rm -rf node_modules package-lock.json 2>/dev/null || true
    
    if ! timeout 180 npm install --production --no-audit --no-fund >>"$LOG_FILE" 2>&1; then
      log_warning "⚠️ Server npm install failed or timed out, trying with --force..."
      timeout 120 npm install --production --force --no-audit --no-fund >>"$LOG_FILE" 2>&1 || {
        log_error "❌ Server dependencies installation failed"
        popd >/dev/null
        return 1
      }
    fi

    # Apply security fixes for server dependencies
    log_info "🔒 Applying server security fixes..."
    npm audit fix >>"$LOG_FILE" 2>&1 || true
    
    popd >/dev/null
    log_success "✅ Server dependencies installed"
  fi

  # Step 3: Install client dependencies and build
  if [[ -d "client" ]]; then
    log_info "Installing client dependencies and building..."
    pushd "client" >/dev/null
    
    # Fix npm cache permissions for client
    sudo chown -R ubuntu:ubuntu ~/.npm 2>/dev/null || true

    # Clean previous build to ensure fresh build with new environment
    rm -rf build node_modules/.cache 2>/dev/null || true

    # Install client dependencies with timeout
    if [[ -f package-lock.json ]]; then
      timeout 180 npm ci --no-audit --no-fund >>"$LOG_FILE" 2>&1 || timeout 180 npm install --no-audit --no-fund >>"$LOG_FILE" 2>&1
    else
      timeout 180 npm install --include=dev --no-audit --no-fund >>"$LOG_FILE" 2>&1
    fi

    # Skip security fixes to avoid breaking CRA build
    log_info "🔒 Skipping client security fixes to prevent build breakage"

    # Ensure react-scripts is available with timeout
    if [[ ! -x "node_modules/.bin/react-scripts" ]]; then
      log_info "Installing react-scripts safeguard..."
      timeout 120 npm install react-scripts@5.0.1 --save-dev --no-audit --no-fund >>"$LOG_FILE" 2>&1 || true
    fi

    # Build the React application with environment variables
    log_info "Building React application with ${DEPLOYMENT_ENV} configuration..."

    # Use the same API URLs that were set in the client .env file
    local api_url=$(grep "^REACT_APP_API_URL=" .env | cut -d'=' -f2- | tr -d '"')
    local ws_url=$(grep "^REACT_APP_WS_URL=" .env | cut -d'=' -f2- | tr -d '"')

    # Fallback if not found in .env
    if [[ -z "$api_url" ]]; then
      api_url="http://localhost:${SERVER_HTTP_PORT}/api"
      if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
        api_url="${PRODUCTION_API_URL}"
      fi
    fi

    if [[ -z "$ws_url" ]]; then
      ws_url="ws://localhost:${SERVER_HTTP_PORT}"
      if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
        ws_url="${PRODUCTION_WS_URL}"
      fi
    fi

    log_info "Building with API URL: $api_url"
    log_info "Building with WS URL: $ws_url"

    # Build with explicit environment variables
    if ! REACT_APP_API_URL="$api_url" REACT_APP_WS_URL="$ws_url" REACT_APP_USE_HTTPS=false npm run build >>"$LOG_FILE" 2>&1; then
      log_error "❌ React build failed"
      # Show last 20 lines of build output for debugging
      tail -20 "$LOG_FILE"
      popd >/dev/null
      return 1
    fi

    # Verify build output
    if [[ -d "build" ]] && [[ -f "build/index.html" ]]; then
      log_success "✅ React build completed successfully"
      log_info "Build size: $(du -sh build | cut -f1)"
    else
      log_error "❌ React build output not found"
      popd >/dev/null
      return 1
    fi
    
    popd >/dev/null
  fi

  log_success "✅ Application building completed"
}

# =============================================================================
# MAIN BUILD FUNCTION
# =============================================================================
main() {
  log_info "🚀 Starting Application Build Process"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $LOG_FILE"
  
  # Check root privileges
  check_root
  
  # Validate application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    log_error "Please run setup-repository-environment.sh first"
    exit 1
  fi
  
  # Step 1: Verify and install Node.js dependencies
  if ! verify_and_install_node_dependencies; then
    log_error "❌ Node.js dependencies verification failed"
    exit 1
  fi
  
  # Step 2: Apply production patches
  if ! apply_production_patches; then
    log_error "❌ Production patches failed"
    exit 1
  fi
  
  # Step 3: Fix frontend build issues
  if ! fix_frontend_build_issues; then
    log_warning "⚠️ Frontend build fixes had issues, but continuing..."
  fi
  
  # Step 4: Build application
  if ! build_application; then
    log_error "❌ Application building failed"
    exit 1
  fi
  
  # Final validation
  log_info "🔍 Validating build..."
  local validation_errors=0
  
  # Check root node_modules
  if [[ ! -d "$APP_DIR/node_modules" ]]; then
    log_error "❌ Root node_modules not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check server node_modules
  if [[ -d "$APP_DIR/server" ]] && [[ ! -d "$APP_DIR/server/node_modules" ]]; then
    log_error "❌ Server node_modules not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check client build
  if [[ -d "$APP_DIR/client" ]] && [[ ! -d "$APP_DIR/client/build" ]]; then
    log_error "❌ Client build directory not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check critical modules
  cd "$APP_DIR"
  local critical_modules=("pg" "express" "cors")
  for module in "${critical_modules[@]}"; do
    if ! node -e "require('$module')" 2>/dev/null; then
      log_error "❌ Critical module not available: $module"
      validation_errors=$((validation_errors + 1))
    fi
  done
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Application Build Process completed successfully!"
    log_info "✅ Root dependencies installed"
    log_info "✅ Server dependencies installed"
    log_info "✅ Client application built"
    log_info "✅ Production patches applied"
    return 0
  else
    log_error "❌ Application Build Process completed with $validation_errors errors"
    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
