# =============================================================================
# HAULING QR TRIP SYSTEM - OPTIMIZED POSTGRESQL CONFIGURATION
# =============================================================================
# System: 4 vCPU / 8GB RAM VPS
# Optimized for high-concurrency trip operations
# Applied via optimize-system-resources.sh
# =============================================================================

# CONNECTIONS AND AUTHENTICATION
#------------------------------------------------------------------------------
max_connections = 200                   # Concurrent trip operations support
superuser_reserved_connections = 3      # Reserved for superuser connections

# RESOURCE USAGE (except WAL)
#------------------------------------------------------------------------------
# Memory Configuration (optimized for 8GB RAM)
shared_buffers = 2GB                    # 25% of RAM - primary buffer cache
huge_pages = try                        # Use huge pages if available
temp_buffers = 8MB                      # Temporary table buffers
max_prepared_transactions = 0           # Zero disables prepared transactions

# Work Memory (per operation)
work_mem = 16MB                         # RAM/(4*max_connections) rounded up
maintenance_work_mem = 512MB            # 1/16 of RAM for maintenance ops
autovacuum_work_mem = -1                # Use maintenance_work_mem value

# Kernel Resource Usage
max_files_per_process = 1000            # File descriptors per backend
shared_preload_libraries = ''           # Libraries to preload

# WRITE AHEAD LOG
#------------------------------------------------------------------------------
# WAL Configuration (optimized for write-heavy workloads)
wal_level = replica                     # Minimal, replica, or logical
fsync = on                              # Force synchronization
synchronous_commit = on                 # Synchronization level
wal_sync_method = fsync                 # Method for forcing WAL updates
full_page_writes = on                   # Recover from partial page writes
wal_compression = on                    # Compress full-page writes
wal_buffers = 16MB                      # 3% of shared_buffers
wal_writer_delay = 200ms                # WAL writer sleep time
wal_writer_flush_after = 1MB            # WAL writer flush threshold

# Checkpoints
checkpoint_timeout = 5min               # Maximum time between checkpoints
max_wal_size = 2GB                      # Maximum WAL size before checkpoint
min_wal_size = 80MB                     # Minimum WAL size to keep
checkpoint_completion_target = 0.9      # Spread checkpoint I/O over 90%
checkpoint_flush_after = 256kB          # Flush after this much WAL
checkpoint_warning = 30s                # Warn if checkpoints happen too often

# Archiving
archive_mode = off                      # Disable archiving for performance
archive_command = ''                    # Command to archive WAL files

# REPLICATION
#------------------------------------------------------------------------------
max_wal_senders = 10                    # Maximum WAL sender processes
max_replication_slots = 10              # Maximum replication slots
track_commit_timestamp = off            # Collect commit timestamps

# QUERY TUNING
#------------------------------------------------------------------------------
# Planner Method Configuration
enable_bitmapscan = on
enable_hashagg = on
enable_hashjoin = on
enable_indexscan = on
enable_indexonlyscan = on
enable_material = on
enable_mergejoin = on
enable_nestloop = on
enable_seqscan = on
enable_sort = on
enable_tidscan = on

# Planner Cost Constants (SSD optimized)
seq_page_cost = 1.0                     # Sequential page fetch cost
random_page_cost = 1.1                  # Random page fetch cost (SSD)
cpu_tuple_cost = 0.01                   # CPU cost per tuple
cpu_index_tuple_cost = 0.005            # CPU cost per index tuple
cpu_operator_cost = 0.0025              # CPU cost per operator
parallel_tuple_cost = 0.1               # CPU cost per tuple for parallel query
parallel_setup_cost = 1000.0            # Cost of starting parallel workers
min_parallel_table_scan_size = 8MB      # Minimum table size for parallel scan
min_parallel_index_scan_size = 512kB    # Minimum index size for parallel scan
effective_cache_size = 6GB              # 75% of RAM - OS + shared cache

# Genetic Query Optimizer
geqo = on                               # Enable genetic query optimization
geqo_threshold = 12                     # Use GEQO for joins >= this number
geqo_effort = 5                         # GEQO effort (1-10)
geqo_pool_size = 0                      # GEQO pool size (0 = auto)
geqo_generations = 0                    # GEQO generations (0 = auto)
geqo_selection_bias = 2.0               # GEQO selection bias
geqo_seed = 0.0                         # GEQO random seed

# Other Planner Options
default_statistics_target = 100         # Statistics target for ANALYZE
constraint_exclusion = partition        # Enable constraint exclusion
cursor_tuple_fraction = 0.1             # Fraction of cursor tuples to retrieve
from_collapse_limit = 8                 # FROM list size beyond which subqueries
join_collapse_limit = 8                 # FROM list size beyond which JOIN

# REPORTING AND LOGGING
#------------------------------------------------------------------------------
# Where to Log
log_destination = 'stderr'              # Log destination
logging_collector = on                  # Enable logging collector
log_directory = 'log'                   # Log directory
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'  # Log filename pattern
log_file_mode = 0600                    # Log file permissions
log_truncate_on_rotation = off          # Don't truncate logs on rotation
log_rotation_age = 1d                   # Rotate logs daily
log_rotation_size = 10MB                # Rotate logs at 10MB

# When to Log
client_min_messages = notice            # Client message level
log_min_messages = warning              # Server message level
log_min_error_statement = error         # Log statements causing errors
log_min_duration_statement = 1000       # Log slow queries (1 second)

# What to Log
debug_print_parse = off
debug_print_rewritten = off
debug_print_plan = off
debug_pretty_print = on
log_checkpoints = on                    # Log checkpoint activity
log_connections = on                    # Log connections
log_disconnections = on                 # Log disconnections
log_duration = off                      # Log statement durations
log_error_verbosity = default           # Error message verbosity
log_hostname = off                      # Log hostname in connections
log_line_prefix = '%t [%p-%l] %q%u@%d ' # Log line prefix
log_lock_waits = on                     # Log lock waits
log_statement = 'none'                  # Log statements (none/ddl/mod/all)
log_replication_commands = off          # Log replication commands
log_temp_files = 10MB                   # Log temp files >= this size

# RUNTIME STATISTICS
#------------------------------------------------------------------------------
# Query/Index Statistics Collector
track_activities = on                   # Track running commands
track_counts = on                       # Track table/index access counts
track_io_timing = on                    # Track I/O timing
track_functions = none                  # Track function call counts
track_activity_query_size = 1024        # Query text size to track
stats_temp_directory = 'pg_stat_tmp'    # Temporary statistics directory

# Statistics Monitoring
log_parser_stats = off
log_planner_stats = off
log_executor_stats = off
log_statement_stats = off

# AUTOVACUUM PARAMETERS
#------------------------------------------------------------------------------
autovacuum = on                         # Enable autovacuum
log_autovacuum_min_duration = 0         # Log autovacuum activity
autovacuum_max_workers = 3              # Maximum autovacuum workers
autovacuum_naptime = 1min               # Sleep time between runs
autovacuum_vacuum_threshold = 50        # Minimum number of tuple updates
autovacuum_analyze_threshold = 50       # Minimum number of tuple inserts
autovacuum_vacuum_scale_factor = 0.2    # Fraction of table size
autovacuum_analyze_scale_factor = 0.1   # Fraction of table size
autovacuum_freeze_max_age = 200000000   # Maximum XID age before forced vacuum
autovacuum_multixact_freeze_max_age = 400000000  # Maximum multixact age
autovacuum_vacuum_cost_delay = 20ms     # Vacuum cost delay
autovacuum_vacuum_cost_limit = -1       # Vacuum cost limit (-1 = use vacuum_cost_limit)

# CLIENT CONNECTION DEFAULTS
#------------------------------------------------------------------------------
# Statement Behavior
search_path = '"$user", public'         # Schema search path
default_tablespace = ''                 # Default tablespace
temp_tablespaces = ''                   # Temporary tablespaces
check_function_bodies = on              # Check function bodies during CREATE
default_transaction_isolation = 'read committed'  # Transaction isolation
default_transaction_read_only = off     # Read-only transactions
default_transaction_deferrable = off    # Deferrable transactions
session_replication_role = 'origin'     # Replication role

# Locale and Formatting
datestyle = 'iso, mdy'                  # Date style
intervalstyle = 'postgres'              # Interval style
timezone = 'UTC'                        # Time zone
timezone_abbreviations = 'Default'      # Time zone abbreviations
extra_float_digits = 0                  # Extra float digits
client_encoding = sql_ascii             # Client encoding

# Shared Library Preloading
shared_preload_libraries = ''           # Libraries to preload
local_preload_libraries = ''            # Libraries to preload per connection
session_preload_libraries = ''          # Libraries to preload per session

# Other Defaults
dynamic_library_path = '$libdir'        # Dynamic library path
gin_fuzzy_search_limit = 0              # GIN fuzzy search limit

# LOCK MANAGEMENT
#------------------------------------------------------------------------------
deadlock_timeout = 1s                  # Deadlock detection timeout
max_locks_per_transaction = 64          # Maximum locks per transaction
max_pred_locks_per_transaction = 64     # Maximum predicate locks per transaction
max_pred_locks_per_relation = -2        # Maximum predicate locks per relation
max_pred_locks_per_page = 2             # Maximum predicate locks per page

# VERSION/PLATFORM COMPATIBILITY
#------------------------------------------------------------------------------
array_nulls = on                        # Array null handling
backslash_quote = safe_encoding         # Backslash quote handling
default_with_oids = off                 # Create tables with OIDs
escape_string_warning = on              # Escape string warnings
lo_compat_privileges = off              # Large object compatibility
operator_precedence_warning = off       # Operator precedence warnings
quote_all_identifiers = off             # Quote all identifiers
sql_inheritance = on                    # SQL inheritance
standard_conforming_strings = on        # Standard conforming strings
synchronize_seqscans = on               # Synchronize sequential scans
transform_null_equals = off             # Transform NULL equals

# ERROR HANDLING
#------------------------------------------------------------------------------
exit_on_error = off                     # Exit on error
restart_after_crash = on                # Restart after crash

# CONFIG FILE INCLUDES
#------------------------------------------------------------------------------
# These options allow settings to be loaded from files other than the
# default postgresql.conf.
#include_dir = 'conf.d'                 # Include files from directory
#include_if_exists = 'exists.conf'      # Include file if it exists
#include = 'special.conf'               # Include file

# CUSTOMIZED OPTIONS
#------------------------------------------------------------------------------
# Add settings for extensions here

# Performance Extensions (uncomment if installed)
#shared_preload_libraries = 'pg_stat_statements'
#pg_stat_statements.max = 10000
#pg_stat_statements.track = all
