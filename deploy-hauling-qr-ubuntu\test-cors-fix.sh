#!/bin/bash

# =============================================================================
# CORS FIX VERIFICATION SCRIPT
# =============================================================================
# Version: 1.0.0
# Description: Test CORS functionality for Hauling QR Trip System
# Usage: ./test-cors-fix.sh [domain]
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================
readonly SCRIPT_NAME="$(basename "${BASH_SOURCE[0]}")"
readonly DOMAIN="${1:-truckhaul.top}"
readonly API_DOMAIN="api.${DOMAIN}"
readonly FRONTEND_DOMAIN="https://${DOMAIN}"
readonly API_BASE_URL="https://${API_DOMAIN}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# =============================================================================
# TEST FUNCTIONS
# =============================================================================
test_preflight_request() {
  log_info "Testing CORS preflight request..."
  
  local url="${API_BASE_URL}/api/auth/login"
  local origin="$FRONTEND_DOMAIN"
  
  echo ""
  echo "Request Details:"
  echo "  URL: $url"
  echo "  Origin: $origin"
  echo "  Method: OPTIONS"
  echo ""
  
  local response
  local exit_code=0
  
  # Perform preflight request
  response=$(curl -s -i -X OPTIONS "$url" \
    -H "Origin: $origin" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: content-type,authorization" \
    2>/dev/null) || exit_code=$?
  
  if [[ $exit_code -ne 0 ]]; then
    log_error "Failed to perform preflight request (curl exit code: $exit_code)"
    return 1
  fi
  
  echo "Response:"
  echo "$response"
  echo ""
  
  # Check response status
  local status_line
  status_line=$(echo "$response" | head -n1)
  
  if echo "$status_line" | grep -q "204"; then
    log_success "✅ Preflight returned 204 No Content"
  else
    log_error "❌ Preflight did not return 204 status"
    log_error "   Status: $status_line"
    return 1
  fi
  
  # Check required CORS headers
  local headers_ok=true
  
  if echo "$response" | grep -qi "access-control-allow-origin.*$DOMAIN"; then
    log_success "✅ Access-Control-Allow-Origin header present and correct"
  else
    log_error "❌ Access-Control-Allow-Origin header missing or incorrect"
    headers_ok=false
  fi
  
  if echo "$response" | grep -qi "access-control-allow-credentials.*true"; then
    log_success "✅ Access-Control-Allow-Credentials header present"
  else
    log_error "❌ Access-Control-Allow-Credentials header missing"
    headers_ok=false
  fi
  
  if echo "$response" | grep -qi "access-control-allow-methods"; then
    log_success "✅ Access-Control-Allow-Methods header present"
  else
    log_error "❌ Access-Control-Allow-Methods header missing"
    headers_ok=false
  fi
  
  if echo "$response" | grep -qi "access-control-allow-headers"; then
    log_success "✅ Access-Control-Allow-Headers header present"
  else
    log_error "❌ Access-Control-Allow-Headers header missing"
    headers_ok=false
  fi
  
  if [[ "$headers_ok" == "true" ]]; then
    log_success "✅ All required CORS headers present"
    return 0
  else
    log_error "❌ Some required CORS headers missing"
    return 1
  fi
}

test_actual_request() {
  log_info "Testing actual API request..."
  
  local url="${API_BASE_URL}/api/health"
  local origin="$FRONTEND_DOMAIN"
  
  echo ""
  echo "Request Details:"
  echo "  URL: $url"
  echo "  Origin: $origin"
  echo "  Method: GET"
  echo ""
  
  local response
  local exit_code=0
  
  # Perform actual request
  response=$(curl -s -i -X GET "$url" \
    -H "Origin: $origin" \
    -H "Content-Type: application/json" \
    2>/dev/null) || exit_code=$?
  
  if [[ $exit_code -ne 0 ]]; then
    log_error "Failed to perform actual request (curl exit code: $exit_code)"
    return 1
  fi
  
  echo "Response:"
  echo "$response"
  echo ""
  
  # Check response status
  local status_line
  status_line=$(echo "$response" | head -n1)
  
  if echo "$status_line" | grep -q "200"; then
    log_success "✅ API request returned 200 OK"
  else
    log_error "❌ API request did not return 200 status"
    log_error "   Status: $status_line"
    return 1
  fi
  
  # Check CORS headers in response
  if echo "$response" | grep -qi "access-control-allow-origin"; then
    log_success "✅ CORS headers present in API response"
  else
    log_warning "⚠️ CORS headers not present in API response (may be handled by proxy)"
  fi
  
  return 0
}

test_login_endpoint() {
  log_info "Testing login endpoint (should fail with invalid credentials but CORS should work)..."
  
  local url="${API_BASE_URL}/api/auth/login"
  local origin="$FRONTEND_DOMAIN"
  
  echo ""
  echo "Request Details:"
  echo "  URL: $url"
  echo "  Origin: $origin"
  echo "  Method: POST"
  echo "  Data: {\"email\":\"test\",\"password\":\"test\"}"
  echo ""
  
  local response
  local exit_code=0
  
  # Perform login request (expect failure but CORS should work)
  response=$(curl -s -i -X POST "$url" \
    -H "Origin: $origin" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer dummy" \
    --data '{"email":"test","password":"test"}' \
    2>/dev/null) || exit_code=$?
  
  if [[ $exit_code -ne 0 ]]; then
    log_error "Failed to perform login request (curl exit code: $exit_code)"
    return 1
  fi
  
  echo "Response:"
  echo "$response"
  echo ""
  
  # Check that we got a response (even if it's an error)
  local status_line
  status_line=$(echo "$response" | head -n1)
  
  if echo "$status_line" | grep -qE "(400|401|422|500)"; then
    log_success "✅ Login endpoint responded (expected failure with invalid credentials)"
  elif echo "$status_line" | grep -q "200"; then
    log_warning "⚠️ Login endpoint returned 200 (unexpected with dummy credentials)"
  else
    log_error "❌ Login endpoint returned unexpected status: $status_line"
    return 1
  fi
  
  return 0
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  echo "=============================================================================="
  echo "🧪 CORS FIX VERIFICATION - HAULING QR TRIP SYSTEM"
  echo "=============================================================================="
  echo ""
  echo "Domain: $DOMAIN"
  echo "API Domain: $API_DOMAIN"
  echo "Frontend Domain: $FRONTEND_DOMAIN"
  echo "API Base URL: $API_BASE_URL"
  echo ""
  echo "=============================================================================="
  echo ""
  
  local tests_passed=0
  local tests_failed=0
  
  # Test 1: Preflight request
  echo "🔍 TEST 1: CORS Preflight Request"
  echo "=============================================================================="
  if test_preflight_request; then
    ((tests_passed++))
    log_success "✅ TEST 1 PASSED: CORS preflight working correctly"
  else
    ((tests_failed++))
    log_error "❌ TEST 1 FAILED: CORS preflight not working"
  fi
  echo ""
  
  # Test 2: Actual API request
  echo "🔍 TEST 2: Actual API Request"
  echo "=============================================================================="
  if test_actual_request; then
    ((tests_passed++))
    log_success "✅ TEST 2 PASSED: API request working correctly"
  else
    ((tests_failed++))
    log_error "❌ TEST 2 FAILED: API request not working"
  fi
  echo ""
  
  # Test 3: Login endpoint
  echo "🔍 TEST 3: Login Endpoint CORS"
  echo "=============================================================================="
  if test_login_endpoint; then
    ((tests_passed++))
    log_success "✅ TEST 3 PASSED: Login endpoint CORS working"
  else
    ((tests_failed++))
    log_error "❌ TEST 3 FAILED: Login endpoint CORS not working"
  fi
  echo ""
  
  # Summary
  echo "=============================================================================="
  echo "📊 TEST SUMMARY"
  echo "=============================================================================="
  echo "Tests Passed: $tests_passed"
  echo "Tests Failed: $tests_failed"
  echo ""
  
  if [[ $tests_failed -eq 0 ]]; then
    log_success "🎉 ALL TESTS PASSED - CORS is working correctly!"
    echo ""
    echo "✅ Your Hauling QR Trip System should now work properly with:"
    echo "   - Login functionality"
    echo "   - API requests from the frontend"
    echo "   - Cross-origin requests with credentials"
    echo ""
    return 0
  else
    log_error "❌ SOME TESTS FAILED - CORS needs attention"
    echo ""
    echo "🔧 Next steps:"
    echo "   1. Check if Cloudflare Worker is deployed"
    echo "   2. Verify NGINX configuration"
    echo "   3. Review deployment logs"
    echo "   4. Consult CORS_FIX_GUIDE.md"
    echo ""
    return 1
  fi
}

# =============================================================================
# COMMAND LINE HANDLING
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
