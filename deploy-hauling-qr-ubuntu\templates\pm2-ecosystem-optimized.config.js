/**
 * PM2 Ecosystem Configuration - Optimized for 4 vCPU / 8GB RAM VPS
 * Hauling QR Trip System - High Performance Cluster Setup
 * 
 * This configuration optimizes PM2 for:
 * - 4 worker instances (one per vCPU)
 * - Cluster mode with load balancing
 * - Memory limits to prevent system overload
 * - Automatic restart and monitoring
 * - Production-ready logging and error handling
 */

module.exports = {
  apps: [{
    // Application Identity
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    
    // Cluster Configuration (optimized for 4 vCPU system)
    instances: 4,                        // One instance per vCPU
    exec_mode: 'cluster',                // Enable cluster mode with load balancing
    instance_var: 'INSTANCE_ID',         // Environment variable for instance identification
    
    // Environment Variables
    env: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,
      INSTANCE_ID: 0,                    // Will be overridden by PM2
      
      // Performance Settings
      UV_THREADPOOL_SIZE: 16,            // Increase libuv thread pool (4 * vCPUs)
      NODE_OPTIONS: '--max-old-space-size=1400 --optimize-for-size',
      
      // Database Connection Pool (per instance)
      DB_POOL_MAX: 50,                   // 200 total / 4 instances = 50 per instance
      DB_POOL_MIN: 12,                   // 5 * 4 instances / 4 = ~12 per instance
      
      // Logging Configuration
      LOG_LEVEL: 'info',
      ENABLE_REQUEST_LOGGING: false,     // Disable verbose request logging in production
      ENABLE_PERFORMANCE_LOGGING: true   // Enable performance monitoring
    },
    
    // Production Environment (explicit override)
    env_production: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,
      LOG_LEVEL: 'warn',                 // Reduce logging in production
      ENABLE_REQUEST_LOGGING: false,
      ENABLE_PERFORMANCE_LOGGING: true
    },
    
    // Development Environment (for testing)
    env_development: {
      NODE_ENV: 'development',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,
      LOG_LEVEL: 'debug',
      ENABLE_REQUEST_LOGGING: true,
      ENABLE_PERFORMANCE_LOGGING: true
    },
    
    // Memory Management (critical for 8GB system)
    max_memory_restart: '1500M',         // Restart if instance exceeds 1.5GB
    node_args: '--max-old-space-size=1400 --optimize-for-size',
    
    // Process Management
    kill_timeout: 5000,                  // Time to wait before force killing (5s)
    listen_timeout: 10000,               // Time to wait for app to listen (10s)
    restart_delay: 1000,                 // Delay between restarts (1s)
    max_restarts: 10,                    // Maximum restarts within min_uptime
    min_uptime: '10s',                   // Minimum uptime before considering stable
    
    // Restart Behavior
    autorestart: true,                   // Automatically restart on crash
    watch: false,                        // Disable file watching in production
    ignore_watch: [                      // Files to ignore if watch is enabled
      'node_modules',
      'logs',
      '.git',
      'uploads',
      'temp',
      '*.log'
    ],
    
    // Logging Configuration
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,                    // Merge logs from all instances
    
    // Advanced Options
    source_map_support: false,           // Disable source maps in production
    disable_trace: true,                 // Disable tracing for performance
    
    // Health Monitoring
    health_check_grace_period: 3000,     // Grace period for health checks (3s)
    
    // Cluster-specific Settings
    wait_ready: true,                    // Wait for ready signal from app
    listen_timeout: 10000,               // Timeout for listen event
    kill_retry_time: 100,                // Retry interval for kill signal
    
    // Error Handling
    panic_action: 'restart',             // Action on panic (restart instance)
    
    // Performance Monitoring
    pmx: true,                           // Enable PMX monitoring
    
    // Instance Variables for Load Balancing
    env_diff: {
      INSTANCE_ID: 0                     // Will be set by PM2 for each instance
    }
  }],
  
  // Deployment Configuration (optional)
  deploy: {
    production: {
      user: 'ubuntu',
      host: ['truckhaul.top'],
      ref: 'origin/main',
      repo: 'https://github.com/mightybadz18/hauling-qr-trip-management.git',
      path: '/var/www/hauling-qr-system',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  },
  
  // Global PM2 Settings
  pm2_serve_path: './client/build',      // Serve static files (if needed)
  pm2_serve_port: 3000,                  // Static file server port
  pm2_serve_spa: true,                   // Single Page Application mode
  
  // Monitoring and Alerts (if PM2 Plus is used)
  monitoring: {
    http: true,                          // Enable HTTP monitoring
    https: false,                        // Disable HTTPS monitoring
    port: false,                         // Don't monitor specific ports
    
    // Custom metrics
    custom_probes: [
      {
        name: 'Memory Usage',
        value: 'process.memoryUsage().heapUsed'
      },
      {
        name: 'Event Loop Lag',
        value: 'Math.round(require("@nodejs/event-loop-lag")())'
      }
    ]
  }
};

/**
 * Performance Notes:
 * 
 * 1. Memory Allocation:
 *    - 4 instances × 1.5GB = 6GB maximum application memory
 *    - Leaves 2GB for system, PostgreSQL, and NGINX
 *    - Each instance limited to 1.4GB heap + 100MB overhead
 * 
 * 2. CPU Utilization:
 *    - One instance per vCPU ensures optimal CPU usage
 *    - Cluster mode provides automatic load balancing
 *    - No CPU affinity needed - OS scheduler handles distribution
 * 
 * 3. Database Connections:
 *    - Total pool: 200 connections (PostgreSQL max_connections)
 *    - Per instance: 50 connections maximum
 *    - Prevents connection exhaustion
 * 
 * 4. Restart Strategy:
 *    - Graceful restarts with 5-second timeout
 *    - Memory-based restarts prevent memory leaks
 *    - Maximum 10 restarts in 10 seconds prevents restart loops
 * 
 * 5. Logging:
 *    - Merged logs for easier monitoring
 *    - Separate error logs for debugging
 *    - Timestamped entries for correlation
 * 
 * 6. Monitoring:
 *    - Built-in health checks
 *    - Memory and event loop monitoring
 *    - Ready for PM2 Plus integration
 * 
 * Usage:
 *   pm2 start ecosystem.config.js --env production
 *   pm2 reload ecosystem.config.js --env production
 *   pm2 stop ecosystem.config.js
 *   pm2 delete ecosystem.config.js
 * 
 * Monitoring:
 *   pm2 monit
 *   pm2 logs hauling-qr-server
 *   pm2 show hauling-qr-server
 */
