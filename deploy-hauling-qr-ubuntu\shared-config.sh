#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SHARED CONFIGURATION MODULE
# =============================================================================
# Version: 1.0.0 - Centralized configuration for all deployment modules
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Common variables, functions, and utilities for modular deployment
# =============================================================================

# Prevent multiple sourcing
if [[ "${SHARED_CONFIG_LOADED:-}" == "true" ]]; then
  return 0
fi
readonly SHARED_CONFIG_LOADED="true"

# =============================================================================
# CORE CONFIGURATION VARIABLES
# =============================================================================
readonly VERSION="4.0.0"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly GITHUB_REPO="mightybadz18/hauling-qr-trip-management"
readonly PRODUCTION_DOMAIN="truckhaul.top"

# Application Configuration
readonly APP_NAME="hauling-qr-system"
readonly APP_DIR="/var/www/${APP_NAME}"
readonly DB_NAME="hauling_qr_system"
readonly DB_USER="postgres"
readonly DB_PASSWORD="PostgreSQLPassword123"
readonly JWT_SECRET="hauling_qr_jwt_secret_2025_secure_key_for_production"

# Network Configuration - CLOUDFLARE COMPATIBLE
readonly CLIENT_PORT=3000
readonly SERVER_HTTP_PORT=8080  # Cloudflare-compatible port
readonly SERVER_HTTPS_PORT=8443
readonly SERVER_PORT=8080  # Legacy support

# Production URLs - FIXED for Cloudflare API subdomain proxy
readonly PRODUCTION_API_URL="https://api.truckhaul.top/api"
readonly PRODUCTION_WS_URL="wss://api.truckhaul.top/ws"
readonly PRODUCTION_FRONTEND_URL="https://truckhaul.top"

# Ubuntu User Configuration
readonly UBUNTU_USER="ubuntu"
readonly UBUNTU_HOME="/home/<USER>"

# Environment Variables (can be overridden)
DETECTED_VPS_IP="${DETECTED_VPS_IP:-}"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-development}"
PRESERVE_ARTIFACTS="${PRESERVE_ARTIFACTS:-false}"
GITHUB_PAT="${GITHUB_PAT:-}"
GITHUB_USERNAME="${GITHUB_USERNAME:-}"
MANUAL_IP="${MANUAL_IP:-}"

# WSL Detection
readonly IS_WSL=$(grep -qi microsoft /proc/version && echo "true" || echo "false")

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Initialize logging directory
init_logging() {
  sudo mkdir -p "$LOG_DIR"
  sudo chmod 755 "$LOG_DIR"
  sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true
}

# Timestamp function
ts() { date '+%Y-%m-%d %H:%M:%S'; }

# Base logging function
log() { 
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo "[$(ts)] $*" | tee -a "$log_file"
}

# Colored logging functions
log_info() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$log_file"
}

log_success() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$log_file"
}

log_warning() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$log_file"
}

log_error() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$log_file"
}

log_debug() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$log_file"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Check if running as root
check_root() {
  if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root (use sudo)"
    exit 1
  fi
}

# Check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Wait for service to be ready
wait_for_service() {
  local service_name="$1"
  local max_attempts="${2:-30}"
  local attempt=1
  
  log_info "Waiting for $service_name to be ready..."
  
  while [[ $attempt -le $max_attempts ]]; do
    if systemctl is-active "$service_name" >/dev/null 2>&1; then
      log_success "✅ $service_name is ready"
      return 0
    fi
    
    log_info "Attempt $attempt/$max_attempts: $service_name not ready, waiting..."
    sleep 2
    attempt=$((attempt + 1))
  done
  
  log_error "❌ $service_name failed to become ready after $max_attempts attempts"
  return 1
}

# WSL-compatible service management
wsl_service_start() {
  local service_name="$1"
  local timeout="${2:-15}"
  
  log_info "Starting $service_name (WSL-compatible)..."
  
  # Method 1: Direct service command
  if timeout "$timeout" service "$service_name" start >/dev/null 2>&1; then
    log_success "✅ $service_name started via service command"
    return 0
  fi
  
  # Method 2: Init.d script
  if timeout "$timeout" /etc/init.d/"$service_name" start >/dev/null 2>&1; then
    log_success "✅ $service_name started via init.d"
    return 0
  fi
  
  # Method 3: Systemctl (if available)
  if command_exists systemctl && timeout "$timeout" systemctl start "$service_name" >/dev/null 2>&1; then
    log_success "✅ $service_name started via systemctl"
    return 0
  fi
  
  log_error "❌ Failed to start $service_name using all methods"
  return 1
}

# WSL-compatible service reload
wsl_service_reload() {
  local service_name="$1"
  local timeout="${2:-15}"
  
  log_info "Reloading $service_name (WSL-compatible)..."
  
  # Method 1: Direct service reload
  if timeout "$timeout" service "$service_name" reload >/dev/null 2>&1; then
    log_success "✅ $service_name reloaded via service command"
    return 0
  fi
  
  # Method 2: Service restart
  if timeout "$timeout" service "$service_name" restart >/dev/null 2>&1; then
    log_success "✅ $service_name restarted via service command"
    return 0
  fi
  
  # Method 3: Init.d restart
  if timeout "$timeout" /etc/init.d/"$service_name" restart >/dev/null 2>&1; then
    log_success "✅ $service_name restarted via init.d"
    return 0
  fi
  
  log_error "❌ Failed to reload $service_name using all methods"
  return 1
}

# Cleanup function for error handling
cleanup_on_error() {
  local script_name="${1:-unknown}"
  log_error "❌ Error occurred in $script_name, performing cleanup..."
  
  # Stop any running services that might be in inconsistent state
  service nginx stop >/dev/null 2>&1 || true
  service postgresql stop >/dev/null 2>&1 || true
  
  # Clean up temporary files
  rm -f /tmp/hauling-qr-* 2>/dev/null || true
  
  log_info "🧹 Cleanup completed for $script_name"
}

# Set up error handling for a script
setup_error_handling() {
  local script_name="$1"
  set -euo pipefail
  trap "cleanup_on_error '$script_name'" ERR
}

# =============================================================================
# NETWORK FUNCTIONS
# =============================================================================

# Detect VPS IP address
detect_vps_ip() {
  log_info "🔍 Detecting VPS IP address..."
  
  # Use manual IP if provided
  if [[ -n "$MANUAL_IP" ]]; then
    DETECTED_VPS_IP="$MANUAL_IP"
    log_success "✅ Using manual IP: $DETECTED_VPS_IP"
    return 0
  fi
  
  # Try multiple methods to detect IP
  local ip_methods=(
    "curl -s --connect-timeout 10 https://ipv4.icanhazip.com"
    "curl -s --connect-timeout 10 https://api.ipify.org"
    "curl -s --connect-timeout 10 https://checkip.amazonaws.com"
    "dig +short myip.opendns.com @resolver1.opendns.com"
  )
  
  for method in "${ip_methods[@]}"; do
    log_info "Trying: $method"
    if DETECTED_VPS_IP=$(eval "$method" 2>/dev/null | tr -d '\n\r' | grep -E '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'); then
      if [[ -n "$DETECTED_VPS_IP" ]]; then
        log_success "✅ Detected VPS IP: $DETECTED_VPS_IP"
        return 0
      fi
    fi
  done
  
  log_error "❌ Failed to detect VPS IP address"
  return 1
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Validate environment variables
validate_environment() {
  log_info "🔍 Validating environment configuration..."
  
  local validation_errors=0
  
  # Check required directories
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check database credentials
  if [[ -z "$DB_PASSWORD" ]]; then
    log_error "❌ Database password not set"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check network configuration
  if [[ -z "$DETECTED_VPS_IP" ]]; then
    log_warning "⚠️ VPS IP not detected, some features may not work"
  fi
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ Environment validation passed"
    return 0
  else
    log_error "❌ Environment validation failed with $validation_errors errors"
    return 1
  fi
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Initialize shared configuration
init_shared_config() {
  log_info "🔧 Initializing shared configuration..."
  
  # Initialize logging
  init_logging
  
  # Detect environment
  if [[ "$IS_WSL" == "true" ]]; then
    log_info "🐧 WSL environment detected"
  else
    log_info "🐧 Native Linux environment detected"
  fi
  
  # Set deployment environment if not specified
  if [[ -z "$DEPLOYMENT_ENV" ]]; then
    if [[ -n "$PRODUCTION_DOMAIN" ]] && [[ "$DETECTED_VPS_IP" != "" ]]; then
      DEPLOYMENT_ENV="production"
    else
      DEPLOYMENT_ENV="development"
    fi
  fi
  
  log_info "🏗️ Deployment environment: $DEPLOYMENT_ENV"
  log_success "✅ Shared configuration initialized"
}

# =============================================================================
# REPOSITORY AUTHENTICATION HELPER
# =============================================================================
compose_auth_repo_url() {
  local url="$1"
  local pat="$2"
  # If no PAT, return original URL
  if [[ -z "$pat" ]]; then echo "$url"; return; fi
  # For GitHub HTTPS URLs, always use x-access-token as username and PAT as password
  if [[ "$url" =~ ^https://github.com/ ]]; then
    echo "https://x-access-token:${pat}@github.com/${url#https://github.com/}"
  else
    echo "$url"
  fi
}

# Export important variables for child processes
export APP_DIR DB_NAME DB_USER DB_PASSWORD
export SERVER_HTTP_PORT CLIENT_PORT UBUNTU_USER
export DEPLOYMENT_ENV DETECTED_VPS_IP IS_WSL
export GITHUB_PAT GITHUB_REPO
export PRODUCTION_API_URL PRODUCTION_WS_URL PRODUCTION_FRONTEND_URL

# Initialize when sourced
init_shared_config
