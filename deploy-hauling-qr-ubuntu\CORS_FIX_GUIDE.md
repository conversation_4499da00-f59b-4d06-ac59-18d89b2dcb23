# CORS Fix Guide - Hauling QR Trip System

## Problem Description

The Hauling QR Trip System experiences CORS (Cross-Origin Resource Sharing) failures when deployed behind Cloudflare, specifically:

```
Access to XMLHttpRequest at 'https://api.truckhaul.top/api/auth/login' from origin 'https://truckhaul.top' 
has been blocked by CORS policy: Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

### Root Cause Analysis

1. **Browser Preflight**: Modern browsers send OPTIONS preflight requests for POST requests with JSON content
2. **Cloudflare Interception**: Cloudflare intercepts OPTIONS requests and responds with HTTP 204 but without required CORS headers
3. **Missing Headers**: The preflight response lacks `Access-Control-Allow-Origin` and other required CORS headers
4. **Request Blocking**: <PERSON><PERSON><PERSON> blocks the actual POST request due to failed preflight

### Evidence

- OPTIONS request to `/api/auth/login` returns HTTP 204 from Cloudflare without CORS headers
- GET request to `/api/health` works fine because it doesn't require preflight
- NGINX configuration is correct but never reached for OPTIONS requests

## Solution Overview

This guide provides three complementary solutions:

1. **Cloudflare Worker** (Recommended) - Handles CORS at the edge
2. **Enhanced NGINX Configuration** - Defense-in-depth fallback
3. **Express.js Fallback** - Application-level backup

## Solution 1: Cloudflare Worker (Recommended)

### Automated Deployment Preparation

The deployment system now includes Phase 10 which prepares Cloudflare Worker files:

```bash
# Run the enhanced deployment
./auto-deploy.sh

# Or run just the CORS worker preparation
./10_cloudflare-cors-worker.sh
```

### Manual Worker Deployment

After running the automated preparation:

1. **Install Wrangler CLI**:
   ```bash
   npm install -g wrangler
   ```

2. **Authenticate with Cloudflare**:
   ```bash
   wrangler auth login
   ```

3. **Deploy the Worker**:
   ```bash
   cd /tmp/hauling-qr-cors-worker
   wrangler deploy
   ```

4. **Verify Deployment**:
   ```bash
   curl -I -H "Origin: https://truckhaul.top" -X OPTIONS https://api.truckhaul.top/api/auth/login
   ```

### Expected Result

```
HTTP/2 204
Access-Control-Allow-Origin: https://truckhaul.top
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token
Access-Control-Max-Age: 1728000
```

## Solution 2: Enhanced NGINX Configuration

The deployment system automatically configures NGINX with enhanced CORS handling:

### Key Features

- **Comprehensive Headers**: Includes all required CORS headers
- **Preflight Handling**: Proper OPTIONS request handling
- **Cloudflare Compatibility**: Headers match Worker configuration
- **Defense-in-depth**: Works even if Worker fails

### Configuration Location

```
/etc/nginx/sites-available/hauling-qr-system
```

### Manual NGINX Reload

```bash
sudo nginx -t
sudo systemctl reload nginx
```

## Solution 3: Express.js Fallback

The Express.js server includes fallback CORS handling for development and edge cases.

### Configuration File

```
server/server.js
```

### Key Features

- **Development Support**: Allows various development origins
- **Production Fallback**: Provides backup CORS handling
- **Cloudflare Detection**: Detects Cloudflare proxy headers

## Testing and Verification

### 1. Preflight Test

```bash
curl -i -X OPTIONS https://api.truckhaul.top/api/auth/login \
  -H "Origin: https://truckhaul.top" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: content-type,authorization"
```

### 2. Actual Request Test

```bash
curl -i -X POST https://api.truckhaul.top/api/auth/login \
  -H "Origin: https://truckhaul.top" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy" \
  --data '{"email":"test","password":"test"}'
```

### 3. Browser Console Test

Open browser console on https://truckhaul.top and run:

```javascript
fetch('https://api.truckhaul.top/api/health', {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json'
  }
}).then(r => r.json()).then(console.log);
```

## Troubleshooting

### Common Issues

1. **Worker Not Active**: Verify worker deployment in Cloudflare dashboard
2. **Wrong Origin**: Ensure origin matches exactly (https://truckhaul.top)
3. **Cache Issues**: Clear browser cache and Cloudflare cache
4. **DNS Propagation**: Wait for DNS changes to propagate

### Debug Commands

```bash
# Test worker deployment
./10_cloudflare-cors-worker.sh --test

# Check NGINX configuration
sudo nginx -t

# View NGINX logs
sudo tail -f /var/log/nginx/error.log

# Check PM2 logs
pm2 logs hauling-qr-server
```

### Verification Checklist

- [ ] Cloudflare Worker deployed and active
- [ ] NGINX configuration updated and reloaded
- [ ] DNS pointing to correct IP address
- [ ] SSL certificates valid
- [ ] Preflight request returns 204 with CORS headers
- [ ] Actual requests work from browser
- [ ] Login functionality restored

## Security Considerations

### Origin Restrictions

- **No Wildcards**: Never use `*` for `Access-Control-Allow-Origin` with credentials
- **Specific Origins**: Only allow exact origins (https://truckhaul.top)
- **HTTPS Only**: All origins must use HTTPS in production

### Header Security

- **Minimal Headers**: Only expose necessary headers
- **Credential Handling**: Proper `Access-Control-Allow-Credentials` usage
- **Max Age**: Reasonable cache duration (1728000 seconds = 20 days)

## Maintenance

### Regular Checks

1. **Monthly**: Verify CORS headers are present
2. **After Cloudflare Changes**: Re-test preflight requests
3. **After Deployments**: Confirm worker remains active

### Updates

- **Worker Updates**: Redeploy worker when origins change
- **NGINX Updates**: Update configuration for new domains
- **Certificate Renewal**: Ensure SSL certificates remain valid

## Integration with Deployment System

The CORS fix is now integrated into the modular deployment system:

- **Phase 10**: Cloudflare Worker preparation
- **Phase 5**: Enhanced NGINX CORS configuration
- **Automatic**: Express.js fallback configuration

This ensures comprehensive CORS handling across all deployment scenarios.
