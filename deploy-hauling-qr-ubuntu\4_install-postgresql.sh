#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - COMPLETE POSTGRESQL INSTALLATION MODULE
# =============================================================================
# Version: 2.0.0 - Complete PostgreSQL installation with database setup and migrations
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: PostgreSQL 17.x installation, database initialization, and migration execution
# =============================================================================

# Source shared configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh"

# Module-specific configuration
readonly MODULE_NAME="install-postgresql"
readonly LOG_FILE="${LOG_DIR}/postgresql-install-$(date +%Y%m%d-%H%M%S).log"

# Setup error handling
setup_error_handling "$MODULE_NAME"

# =============================================================================
# POSTGRESQL INSTALLATION FUNCTIONS
# =============================================================================
install_postgresql() {
  log_info "🗄️ Installing PostgreSQL 17.x for database compatibility..."

  # Check if PostgreSQL is already installed
  if command_exists psql; then
    local existing_version
    existing_version=$(sudo -u postgres psql --version 2>/dev/null | grep -oP '\d+\.\d+' | head -1 || echo "unknown")
    log_info "PostgreSQL already installed: version $existing_version"
    
    if [[ $(echo "$existing_version >= 17.0" | bc -l 2>/dev/null || echo "0") -eq 1 ]]; then
      log_success "✅ PostgreSQL $existing_version meets requirements (17.0+)"
      return 0
    else
      log_warning "⚠️ PostgreSQL $existing_version is below required version 17.0, upgrading..."
    fi
  fi

  # Add PostgreSQL 17 official repository
  log_info "Adding PostgreSQL 17 official repository..."
  wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add - >/dev/null 2>&1 || {
    log_warning "⚠️ Failed to add PostgreSQL GPG key, trying alternative method..."
    curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg >/dev/null 2>&1
  }

  echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list >/dev/null 2>&1

  # Update package list and install PostgreSQL 17
  log_info "Updating package list and installing PostgreSQL 17..."
  apt-get update >/dev/null 2>&1 || {
    log_error "Failed to update package list for PostgreSQL 17"
    return 1
  }

  apt-get install -y postgresql-17 postgresql-client-17 postgresql-contrib-17 libpq-dev >/dev/null 2>&1 || {
    log_error "Failed to install PostgreSQL 17"
    return 1
  }

  # Ensure PostgreSQL is enabled and started
  systemctl enable postgresql >/dev/null 2>&1 || true
  systemctl start postgresql >/dev/null 2>&1 || true

  log_success "✅ PostgreSQL 17.x installed successfully"
  return 0
}

validate_postgresql_version() {
  log_info "🔍 Validating PostgreSQL installation..."

  # Validate PostgreSQL 17.0+ is installed
  if ! command_exists psql; then
    log_error "PostgreSQL not found. Installation may have failed."
    return 1
  fi

  # Version validation logic
  local PG_VERSION
  PG_VERSION=$(sudo -u postgres psql --version 2>/dev/null | grep -oP '\d+\.\d+' | head -1)
  if [[ -z "$PG_VERSION" ]]; then
    log_error "Could not determine PostgreSQL version"
    return 1
  fi

  log_info "Found PostgreSQL version: $PG_VERSION"
  if [[ $(echo "$PG_VERSION >= 17.0" | bc -l) -eq 0 ]]; then
    log_error "PostgreSQL 17.0+ required for database compatibility, found $PG_VERSION"
    log_error "The database/init.sql was dumped from PostgreSQL 17.4 and requires version 17.0+"
    return 1
  fi

  log_success "✅ PostgreSQL $PG_VERSION meets version requirements (17.0+)"
  return 0
}

configure_postgresql_authentication() {
  log_info "🔐 Configuring PostgreSQL authentication..."

  # Check if Phase 0 optimization has been applied to postgresql.conf
  local pg_config_file
  pg_config_file=$(sudo find /etc/postgresql -name "postgresql.conf" | head -1)

  if [[ -n "$pg_config_file" ]] && sudo grep -q "HAULING QR TRIP SYSTEM - PERFORMANCE OPTIMIZATIONS" "$pg_config_file" 2>/dev/null; then
    log_info "🎯 Phase 0 PostgreSQL performance optimizations detected - preserving settings"
    log_success "✅ PostgreSQL performance configuration preserved (2GB shared_buffers, 200 max_connections)"
  fi

  # Detect PostgreSQL version for config path
  local pg_version
  pg_version=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP 'postgresql-\K[0-9]+' || echo "17")

  # Configure PostgreSQL authentication
  local pg_hba_file="/etc/postgresql/$pg_version/main/pg_hba.conf"
  if [[ -f "$pg_hba_file" ]]; then
    cp "$pg_hba_file" "$pg_hba_file.backup" 2>/dev/null || true

    # Configure authentication methods
    sed -i 's/local   all             postgres                                peer/local   all             postgres                                trust/' "$pg_hba_file"
    sed -i 's/local   all             all                                     peer/local   all             all                                     md5/' "$pg_hba_file"
    sed -i 's/host    all             all             127.0.0.1\/32            scram-sha-256/host    all             all             127.0.0.1\/32            md5/' "$pg_hba_file"
    sed -i 's/host    all             all             ::1\/128                 scram-sha-256/host    all             all             ::1\/128                 md5/' "$pg_hba_file"

    # Reload PostgreSQL configuration
    systemctl reload postgresql 2>/dev/null || true
    sleep 3

    log_success "✅ PostgreSQL authentication configured"
  else
    log_warning "⚠️ pg_hba.conf not found at expected location: $pg_hba_file"
    return 1
  fi

  return 0
}

setup_database() {
  log_info "🗄️ Setting up PostgreSQL database..."

  # Drop existing database if it exists
  if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_info "Dropping existing $DB_NAME database..."

    # Step 1: Prevent new connections
    log_info "Preventing new connections to $DB_NAME..."
    sudo -u postgres psql -c "UPDATE pg_database SET datallowconn = 'false' WHERE datname = '$DB_NAME';" || true

    # Step 2: Terminate all active connections (multiple attempts)
    for attempt in {1..3}; do
      log_info "Terminating active connections to $DB_NAME (attempt $attempt/3)..."

      # Get active connection count
      local active_connections=$(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" | tr -d ' ')

      if [[ "$active_connections" == "0" ]]; then
        log_info "✅ No active connections found"
        break
      fi

      log_info "Found $active_connections active connections, terminating..."

      # Terminate connections
      sudo -u postgres psql -c "
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '$DB_NAME'
          AND pid <> pg_backend_pid();
      " || true

      # Wait longer between attempts
      sleep $((attempt * 2))
    done

    # Step 3: Final connection check and drop
    local remaining_connections=$(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" | tr -d ' ')

    if [[ "$remaining_connections" != "0" ]]; then
      log_warning "⚠️ Still $remaining_connections active connections, attempting force drop..."

      # Nuclear option: restart PostgreSQL to clear all connections
      log_info "Restarting PostgreSQL to clear all connections..."
      sudo systemctl restart postgresql || true
      sleep 5
    fi

    # Now drop the database
    if sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;"; then
      log_success "✅ Database $DB_NAME dropped successfully"
    else
      log_error "❌ Failed to drop database $DB_NAME even after connection termination"
      log_info "Attempting to continue with existing database..."
      # Don't return 1 here, let it continue and try to work with existing database
    fi
  fi

  # Set postgres user password
  log_info "Setting postgres user password..."
  sudo -u postgres psql -c "ALTER USER postgres PASSWORD '${DB_PASSWORD}';" || {
    log_error "Failed to set postgres user password"
    return 1
  }

  # Create fresh database
  log_info "Creating fresh $DB_NAME database..."
  sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" || {
    log_error "Failed to create fresh database"
    return 1
  }

  # Test database connection
  log_info "Testing database connection..."
  if PGPASSWORD="${DB_PASSWORD}" psql -h localhost -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "✅ Database connection successful"
  else
    log_error "❌ Database connection failed"
    return 1
  fi

  log_success "✅ Database setup completed"
  return 0
}

# =============================================================================
# DATABASE INITIALIZATION AND MIGRATIONS
# =============================================================================
initialize_database_with_migrations() {
  log_info "🗄️ Initializing database with migrations..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  if [[ ! -d "database" ]]; then
    log_error "❌ Database directory not found"
    return 1
  fi
  
  # Set up environment for database operations
  export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
  
  # Step 1: Run database/init.sql first (if exists)
  if [[ -f "database/init.sql" ]]; then
    log_info "Step 1: Running database initialization (init.sql)..."
    if PGPASSWORD="${DB_PASSWORD}" psql -h localhost -U "${DB_USER}" -d "${DB_NAME}" -f database/init.sql 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ Database initialization (init.sql) completed successfully"
    else
      log_error "❌ Database initialization (init.sql) failed"
      return 1
    fi
  else
    log_warning "⚠️ database/init.sql not found, skipping initialization"
  fi
  
  # Step 2: Update .env file with correct database credentials
  log_info "Step 2: Ensuring .env file has correct database credentials..."
  if [[ -f ".env" ]]; then
    # Remove any existing database-related lines to avoid conflicts
    sed -i '/^DB_PASSWORD=/d' .env
    sed -i '/^DB_HOST=/d' .env
    sed -i '/^DB_PORT=/d' .env
    sed -i '/^DB_NAME=/d' .env
    sed -i '/^DB_USER=/d' .env

    # Add the correct database configuration with proper quoting
    echo "DB_HOST=localhost" >> .env
    echo "DB_PORT=5432" >> .env
    echo "DB_NAME=${DB_NAME}" >> .env
    echo "DB_USER=${DB_USER}" >> .env
    echo "DB_PASSWORD=\"${DB_PASSWORD}\"" >> .env

    log_success "✅ .env file updated with database credentials"
  else
    log_warning "⚠️ .env file not found, database credentials not updated"
  fi
  
  # Step 3: Verify Node.js pg module is available
  log_info "Step 3: Verifying Node.js pg module..."
  if ! node -e "require('pg')" 2>/dev/null; then
    log_info "Installing pg module..."
    npm install pg --save 2>&1 | tee -a "$LOG_FILE" || {
      log_error "❌ Failed to install pg module"
      return 1
    }
  fi
  log_success "✅ Node.js pg module verified"
  
  # Step 4: Run migrations using run-migration.js
  if [[ -f "database/run-migration.js" ]]; then
    log_info "Step 4: Running database migrations (run-migration.js)..."
    if node database/run-migration.js 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ Database migrations completed successfully"

      # Verify database setup
      local trip_count
      trip_count=$(sudo -u postgres psql -d "${DB_NAME}" -t -c "SELECT COUNT(*) FROM trip_logs;" 2>/dev/null | xargs || echo "0")
      log_info "Database verification: ${trip_count} trips in database"

    else
      log_error "❌ Database migrations failed"
      return 1
    fi
  else
    log_warning "⚠️ database/run-migration.js not found, skipping migrations"
  fi
  
  # Step 5: Test database connection from Node.js
  log_info "Step 5: Testing database connection from Node.js..."
  if node -e "
    const { Pool } = require('pg');
    const pool = new Pool({
      user: '${DB_USER}',
      host: 'localhost',
      database: '${DB_NAME}',
      password: '${DB_PASSWORD}',
      port: 5432,
    });
    pool.query('SELECT NOW()', (err, res) => {
      if (err) {
        console.error('Database connection failed:', err);
        process.exit(1);
      } else {
        console.log('Database connection successful:', res.rows[0]);
        process.exit(0);
      }
      pool.end();
    });
  " 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ Database connection test passed"
  else
    log_error "❌ Database connection test failed"
    return 1
  fi
  
  log_success "✅ Database initialization and migrations completed"
}

# =============================================================================
# MAIN INSTALLATION FUNCTION
# =============================================================================
main() {
  log_info "🚀 Starting Complete PostgreSQL Installation"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $LOG_FILE"

  # Check root privileges
  check_root

  # Validate application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    log_error "Please run setup-repository-environment.sh first"
    exit 1
  fi

  # Step 1: Install PostgreSQL 17.x
  if ! install_postgresql; then
    log_error "❌ PostgreSQL installation failed"
    exit 1
  fi

  # Step 2: Validate PostgreSQL version
  if ! validate_postgresql_version; then
    log_error "❌ PostgreSQL version validation failed"
    exit 1
  fi

  # Step 3: Configure PostgreSQL authentication
  if ! configure_postgresql_authentication; then
    log_error "❌ PostgreSQL authentication configuration failed"
    exit 1
  fi

  # Step 4: Setup database
  if ! setup_database; then
    log_error "❌ Database setup failed"
    exit 1
  fi

  # Step 5: Initialize database with migrations
  if ! initialize_database_with_migrations; then
    log_error "❌ Database initialization and migrations failed"
    exit 1
  fi

  # Final validation
  log_info "🔍 Validating PostgreSQL installation..."
  local validation_errors=0

  # Check PostgreSQL service
  if ! systemctl is-active postgresql >/dev/null 2>&1; then
    log_error "❌ PostgreSQL service is not active"
    validation_errors=$((validation_errors + 1))
  fi

  # Check database exists
  if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_error "❌ Database $DB_NAME does not exist"
    validation_errors=$((validation_errors + 1))
  fi

  # Check Node.js pg module
  cd "$APP_DIR"
  if ! node -e "require('pg')" 2>/dev/null; then
    log_error "❌ Node.js pg module not available"
    validation_errors=$((validation_errors + 1))
  fi

  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Complete PostgreSQL Installation completed successfully!"
    log_info "✅ PostgreSQL 17.x installed and running"
    log_info "✅ Database $DB_NAME created and initialized"
    log_info "✅ Migrations executed successfully"
    log_info "✅ Node.js database connection verified"
    return 0
  else
    log_error "❌ Complete PostgreSQL Installation completed with $validation_errors errors"
    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
