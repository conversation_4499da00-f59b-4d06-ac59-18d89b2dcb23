#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - MODULAR PM2 INSTALLATION SCRIPT
# =============================================================================
# Version: 1.0.0 - Extracted from auto-deploy.sh
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Self-contained PM2 installation, ecosystem configuration, and process management
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="install-pm2.sh"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/pm2-install-$(date +%Y%m%d-%H%M%S).log"

# Application Configuration
readonly APP_DIR="/var/www/hauling-qr-system"
readonly UBUNTU_USER="ubuntu"
readonly UBUNTU_HOME="/home/<USER>"

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================
cleanup_on_error() {
  local exit_code=$?
  if [[ $exit_code -ne 0 ]]; then
    log_error "PM2 installation failed with exit code $exit_code"
    log_error "Check log file: $LOG_FILE"
  fi
  exit $exit_code
}

trap cleanup_on_error ERR

# =============================================================================
# PM2 INSTALLATION FUNCTIONS
# =============================================================================
install_pm2() {
  log_info "📦 Installing PM2 process manager..."

  # Check if PM2 is already installed with enhanced health check
  if command -v pm2 >/dev/null 2>&1; then
    log_info "PM2 already installed: $(pm2 -v)"

    # Enhanced PM2 health check and fixes from backup script
    log_info "Performing comprehensive PM2 health check..."

    # Fix PM2 permissions first
    if [[ -d "/home/<USER>/.pm2" ]]; then
      log_info "Fixing PM2 permissions for ubuntu user..."
      sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2/ 2>/dev/null || true
      chmod -R 755 /home/<USER>/.pm2/ 2>/dev/null || true
    fi

    local pm2_status_output
    pm2_status_output=$(pm2 status 2>&1 || echo "")

    # Check for permission issues
    if echo "$pm2_status_output" | grep -q "Permission denied"; then
      log_warning "⚠️ PM2 permission issues detected - attempting fix..."
      sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2/ 2>/dev/null || true
      pm2 kill 2>/dev/null || true
      sleep 2
      pm2_status_output=$(pm2 status 2>&1 || echo "")
    fi

    # Check for version mismatch (critical issue from backup script)
    if echo "$pm2_status_output" | grep -q "In-memory PM2 is out-of-date"; then
      log_warning "⚠️ PM2 version mismatch detected - updating PM2..."

      # Update PM2 to sync versions with error handling
      log_info "Running PM2 update to synchronize versions..."
      if pm2 update >>"$LOG_FILE" 2>&1; then
        log_success "✅ PM2 updated successfully - versions synchronized"
      else
        log_warning "⚠️ PM2 update failed, trying alternative approach..."
        pm2 kill 2>/dev/null || true
        sleep 2
        pm2 ping 2>/dev/null || true
        log_info "PM2 daemon restarted - version should now be synchronized"
      fi
    else
      log_success "✅ PM2 versions are synchronized"
    fi

    return 0
  fi

  # Install PM2 globally with enhanced error handling
  log_info "Installing PM2 globally with timeout protection..."
  if ! timeout 120 npm install -g pm2 >>"$LOG_FILE" 2>&1; then
    log_warning "⚠️ PM2 global installation failed or timed out, trying alternative method..."
    # Try with sudo if needed
    if ! timeout 120 sudo npm install -g pm2 >>"$LOG_FILE" 2>&1; then
      log_error "❌ PM2 installation failed completely after timeout"
      return 1
    fi
  fi

  # Verify PM2 installation
  if command -v pm2 >/dev/null 2>&1; then
    log_success "✅ PM2 installed successfully: $(pm2 -v)"
  else
    log_error "❌ PM2 installation verification failed"
    return 1
  fi

  # Initial PM2 setup
  log_info "Initializing PM2 for ubuntu user..."
  sudo -u ubuntu pm2 ping >/dev/null 2>&1 || true

  log_success "✅ PM2 installation and setup completed"
  return 0
}

fix_pm2_permissions() {
  log_info "🔧 Fixing PM2 permissions and configuration..."

  # Fix PM2 permissions for ubuntu user
  if [[ -d "${UBUNTU_HOME}/.pm2" ]]; then
    log_info "Fixing PM2 permissions for ubuntu user..."
    sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
    chmod -R 755 "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
  fi

  # Check for PM2 version consistency
  local pm2_check_output
  pm2_check_output=$(pm2 status 2>&1 || echo "")

  # Handle permission issues
  if echo "$pm2_check_output" | grep -q "Permission denied"; then
    log_warning "⚠️ PM2 permission issues detected - fixing..."
    sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
    pm2 kill 2>/dev/null || true
    sleep 2
  fi

  # Check for version mismatch
  if echo "$pm2_check_output" | grep -q "In-memory PM2 is out-of-date"; then
    log_warning "⚠️ PM2 version mismatch detected - updating..."
    if pm2 update >>"$LOG_FILE" 2>&1; then
      log_success "✅ PM2 updated successfully"
    else
      log_warning "⚠️ PM2 update failed, trying daemon restart..."
      pm2 kill 2>/dev/null || true
      sleep 2
      pm2 ping 2>/dev/null || true
    fi
  fi

  log_success "✅ PM2 permissions and configuration fixed"
  return 0
}

create_pm2_ecosystem() {
  log_info "📝 Creating PM2 ecosystem configuration..."

  # Create default ecosystem.config.js
  # Note: Service-specific optimizations will be applied in Phase 11 (11_service-optimization.sh)
  cat >"$APP_DIR/ecosystem.config.js" <<'EOF'
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false
    },
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

  # Set proper permissions
  sudo chown root:root "$APP_DIR/ecosystem.config.js"
  sudo chmod 644 "$APP_DIR/ecosystem.config.js"

  log_success "✅ PM2 ecosystem configuration created"
  return 0
}

ensure_app_directories() {
  log_info "📁 Ensuring application directories exist..."

  # Create necessary directories
  sudo mkdir -p "$APP_DIR/server/logs" "$APP_DIR/server/uploads"
  sudo chown -R root:root "$APP_DIR/server"
  sudo chmod -R 755 "$APP_DIR/server"

  log_success "✅ Application directories created"
  return 0
}

create_rate_limit_fix() {
  log_info "🔧 Skipping rate-limit-fix creation - using native express-rate-limit..."

  # This function now does nothing - we use the native express-rate-limit module
  # The server.js file already has the correct import: require('express-rate-limit')
  # No modifications needed - just ensure dependencies are installed via npm install

  log_success "✅ Using native express-rate-limit module"
  return 0
}

configure_pm2_service() {
  log_info "🚀 Configuring PM2 service..."

  # Change to application directory
  pushd "$APP_DIR" >/dev/null

  # Stop any existing PM2 processes
  pm2 delete hauling-qr-server 2>/dev/null || true
  pm2 kill 2>/dev/null || true

  # Ensure ecosystem.config.js exists
  if [[ ! -f "ecosystem.config.js" ]]; then
    log_error "❌ ecosystem.config.js not found in $APP_DIR"
    popd >/dev/null
    return 1
  fi

  # Start PM2 application with explicit configuration
  log_info "Starting PM2 with ecosystem.config.js"
  if pm2 start ecosystem.config.js --env production; then
    log_success "✅ PM2 application started successfully"
  else
    log_warning "⚠️ Failed to start PM2 application, trying alternative method"
    # Try alternative startup method
    log_info "Trying alternative PM2 startup method"
    if pm2 start server/server.js --name hauling-qr-server --env production; then
      log_success "✅ PM2 application started with alternative method"
    else
      log_error "❌ PM2 startup failed completely"
      popd >/dev/null
      return 1
    fi
  fi

  # Save PM2 configuration
  pm2 save

  # Enable PM2 startup (systemd) for root
  pm2 startup systemd -u root --hp /root >/dev/null 2>&1 || true

  # Fix PM2 ownership issues
  log_info "🔧 Fixing PM2 ownership for ubuntu user access..."
  sudo chown ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/rpc.sock" "${UBUNTU_HOME}/.pm2/pub.sock" 2>/dev/null || true
  sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true

  # Restart with updated environment
  pm2 restart hauling-qr-server --update-env 2>/dev/null || true
  sleep 3

  popd >/dev/null

  log_success "✅ PM2 service configured successfully"
  return 0
}

verify_pm2_service() {
  log_info "🔍 Verifying PM2 service status..."

  # Check if PM2 process is running
  if pm2 list | grep -q "hauling-qr-server.*online"; then
    log_success "✅ PM2 hauling-qr-server is running and verified"
    return 0
  else
    log_warning "⚠️ PM2 hauling-qr-server not running properly"
    log_info "PM2 process list:"
    pm2 list || true
    log_info "PM2 logs (last 10 lines):"
    pm2 logs hauling-qr-server --lines 10 || true

    # Try one more time with ubuntu user
    log_info "Attempting to start PM2 as ubuntu user..."
    if sudo -u ubuntu pm2 start "$APP_DIR/ecosystem.config.js" --env production >/dev/null 2>&1; then
      log_success "✅ PM2 started successfully as ubuntu user"
      return 0
    else
      log_error "❌ Failed to start PM2 as ubuntu user"
      return 1
    fi
  fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  log_info "🚀 Starting PM2 installation for Hauling QR Trip System..."
  log_info "Script: $SCRIPT_NAME"
  log_info "Log file: $LOG_FILE"

  # Step 1: Install PM2
  if ! install_pm2; then
    log_error "❌ PM2 installation failed"
    exit 1
  fi

  # Step 2: Fix PM2 permissions
  if ! fix_pm2_permissions; then
    log_error "❌ PM2 permissions fix failed"
    exit 1
  fi

  # Step 3: Create PM2 ecosystem configuration
  if ! create_pm2_ecosystem; then
    log_error "❌ PM2 ecosystem creation failed"
    exit 1
  fi

  # Step 4: Ensure application directories
  if ! ensure_app_directories; then
    log_error "❌ Application directory setup failed"
    exit 1
  fi

  # Step 5: Create rate limiting fix
  if ! create_rate_limit_fix; then
    log_error "❌ Rate limiting fix creation failed"
    exit 1
  fi

  # Step 6: Configure PM2 service
  if ! configure_pm2_service; then
    log_error "❌ PM2 service configuration failed"
    exit 1
  fi

  # Step 7: Verify PM2 service
  if ! verify_pm2_service; then
    log_error "❌ PM2 service verification failed"
    exit 1
  fi

  log_success "✅ PM2 installation and configuration completed successfully"
  log_info "📋 Installation Summary:"
  log_info "   - PM2 process manager installed and configured"
  log_info "   - Ecosystem configuration created"
  log_info "   - Application directories set up"
  log_info "   - Rate limiting fix applied"
  log_info "   - Service started and verified"

  return 0
}

# Execute main function
main "$@"
